<template>
	<div class="fixed inset-0 z-[9999] flex items-center justify-center bg-black/80 backdrop-blur-sm">
		<!-- 中心仪式动画容器 -->
		<div class="relative h-56 w-56">
			<!-- 外环发光 -->
			<div class="absolute inset-0 animate-pulse border-cyan-400/30 rounded-full shadow-[0_0_40px_10px_rgba(34,211,238,0.25)] border-2"></div>

			<!-- 旋转线框环 -->
			<div class="animate-rotate-slow absolute inset-4 border border-cyan-300/50 rounded-full"></div>
			<div class="animate-rotate-rev absolute inset-2 border border-cyan-500/40 rounded-full"></div>

			<!-- 扫描雷达 -->
			<div class="absolute inset-6 overflow-hidden rounded-full">
				<div class="animate-radar absolute inset-0 bg-[conic-gradient(from_0deg,#22d3ee80_0%,transparent_60%)]"></div>
				<div class="absolute inset-0 bg-[radial-gradient(transparent_60%,rgba(34,211,238,0.08)_61%_70%,transparent_71%)]"></div>
			</div>

			<!-- 中心能量球 -->
			<div
				class="absolute left-1/2 top-1/2 h-10 w-10 animate-ping rounded-full bg-cyan-400 shadow-[0_0_30px_10px_rgba(34,211,238,0.6)] -translate-x-1/2 -translate-y-1/2"
			></div>

			<!-- 文案 -->
			<div class="absolute left-1/2 select-none text-center -bottom-12 -translate-x-1/2">
				<div
					class="mb-1 whitespace-nowrap from-cyan-300 to-blue-400 bg-gradient-to-r bg-clip-text text-transparent font-semibold tracking-widest text-lg"
				>
					正在切换电厂
				</div>
				<div class="text-cyan-200/70 tracking-widest text-xs">LOADING...</div>
			</div>
		</div>

		<!-- 背景网格 -->
		<div class="pointer-events-none absolute inset-0">
			<div
				class="[background-size:40px_40px] [background:linear-gradient(rgba(34,211,238,0.15)_1px,transparent_1px),linear-gradient(90deg,rgba(34,211,238,0.15)_1px,transparent_1px)] absolute inset-0 opacity-20"
			></div>
			<div class="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(34,211,238,0.15),transparent_60%)]"></div>
		</div>
	</div>
</template>

<script setup lang="ts"></script>

<style scoped>
@keyframes rotate-slow {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}
@keyframes rotate-rev {
	from {
		transform: rotate(360deg);
	}
	to {
		transform: rotate(0deg);
	}
}
@keyframes radar {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}
.animate-rotate-slow {
	animation: rotate-slow 6s linear infinite;
}
.animate-rotate-rev {
	animation: rotate-rev 9s linear infinite;
}
.animate-radar {
	animation: radar 3s linear infinite;
}
</style>
