<script setup lang="ts">
import { modalManager } from './modalManager'

// 定义动画持续时间常量，便于统一管理
const visible = defineModel({
	type: Boolean,
	default: false,
})

// Add overlay visibility state
const overlayVisible = ref(false)

defineProps({
	title: {
		type: String,
		default: '',
	},
	loading: {
		type: Boolean,
		default: false,
	},
	// 点击overlay 时，是否关闭modal
	closeOnOverlayClick: {
		type: Boolean,
		default: true,
	},
	containerClass: {
		type: String,
		default: '',
	},
	titleClass: {
		type: String,
		default: '',
	},
	zIndex: {
		type: Number,
		default: 5,
	},
	showSubmit: {
		type: Boolean,
		default: false,
	},
})
const emit = defineEmits(['close', 'submit'])

// Create modal instance for the manager
const modalInstance = {
	close: () => {
		closeModal()
	},
}

// 保存滚动位置和滚动条宽度
let scrollPosition = 0
let scrollbarWidth = 0

// 计算属性用于确定是否应该禁用滚动
const shouldDisableScroll = computed(() => visible.value || overlayVisible.value)

// 禁用页面滚动
const disableScroll = () => {
	scrollPosition = window.pageYOffset
	// 计算滚动条宽度
	scrollbarWidth = window.innerWidth - document.documentElement.clientWidth

	// 使用 CSS 类来管理样式，而不是直接操作 style
	document.body.classList.add('modal-open')
	document.body.style.setProperty('--scrollbar-width', `${scrollbarWidth}px`)
	document.documentElement.style.setProperty('--scroll-top', `${scrollPosition}px`)
}

// 恢复页面滚动
const enableScroll = () => {
	document.body.classList.remove('modal-open')
	document.body.style.removeProperty('--scrollbar-width')
	document.documentElement.style.removeProperty('--scroll-top')
	window.scrollTo(0, scrollPosition)
}

// 统一的关闭逻辑 - 只在这一个地方处理关闭
const closeModal = () => {
	visible.value = false
	emit('close')
}

// 动画完成后关闭 overlay
const onTransitionEnd = () => {
	if (!visible.value) {
		overlayVisible.value = false
	}
}

// 监听 visible 变化，同步 overlay 状态
watch(
	visible,
	(newVal) => {
		if (newVal) {
			// 打开时立即显示 overlay
			overlayVisible.value = true
		}
	},
	{
		immediate: true,
	},
)

// Register/unregister with the modal manager when visibility changes
watchEffect(() => {
	if (visible.value) {
		modalManager.add(modalInstance)
	} else {
		modalManager.remove(modalInstance)
	}
})

// 监听滚动状态变化
watch(
	shouldDisableScroll,
	(newVal) => {
		if (newVal) {
			disableScroll()
		} else {
			enableScroll()
		}
	},
	{ immediate: true },
)

const modalRef = ref<HTMLElement>()
const handleRef = ref<HTMLElement>()

// 初始化拖拽功能
const { x, y } = useDraggable(handleRef, {
	initialValue: { x: 0, y: 0 },
})

// 监听拖拽位置变化，更新 transform
watch([x, y], () => {
	if (modalRef.value) {
		modalRef.value.style.transform = `translate(calc(-50vw + 50% + ${x.value}px), calc(-50vh + 50% + ${y.value}px))`
	}
})

function handleSubmit() {
	// 查找最近的 form 元素并触发其 submit 事件
	const form = modalRef.value?.querySelector('form')
	if (form) {
		form.dispatchEvent(new Event('submit', { cancelable: true }))
	}
	emit('submit')
}

// 确保组件卸载时清理资源
onBeforeUnmount(() => {
	if (shouldDisableScroll.value) {
		enableScroll()
	}
})
</script>

<template>
	<XOverlay
		v-if="overlayVisible"
		:z-index="zIndex"
		@click.self="
			() => {
				if (closeOnOverlayClick) closeModal()
			}
		"
	>
		<XTransition mode="rebound" @after-leave="onTransitionEnd">
			<div
				v-if="visible"
				ref="modalRef"
				class="fixed z-1 max-h-90% max-w-90% w-50rem flex flex-col rounded-2 bg-white !shadow-md"
				:class="containerClass"
			>
				<div v-loading="loading" class="flex flex-1 flex-col overflow-hidden">
					<div
						ref="handleRef"
						class="flex cursor-move select-none items-center justify-between text-title font-bold text-lg mb-base px-xl py-base border-b-border"
						:class="titleClass"
					>
						<span>{{ title }}</span>
						<XButton class="z-11 !h2rem !w2rem !bg-transparent !p-0" @click="closeModal">
							<div class="i-bi:x-lg !color-aid !text-xl"></div>
						</XButton>
					</div>
					<div class="min-h-0 flex-1 overflow-y-auto px-xl" :class="{ 'pb-base': !showSubmit }">
						<slot></slot>
					</div>
					<div v-if="showSubmit" class="wfull flex justify-end mb-base pt-base px-xl gap-sm">
						<BizButtonsCancel @click="closeModal" />
						<XButton @click="handleSubmit">提 交</XButton>
					</div>
				</div>
			</div>
		</XTransition>
	</XOverlay>
</template>

<style></style>
