<template>
	<!-- Tabs.vue -->
	<div
		class="flex"
		:class="[
			{
				'flex-col': placement === 'top',
				'flex-col-reverse': placement === 'bottom',
				'flex-row': placement === 'left',
				'flex-row-reverse': placement === 'right',
			},
		]"
	>
		<!--整个-->

		<div
			class="relative flex items-center"
			:class="[{ 'flex-row': placement === 'top' || placement === 'bottom', 'flex-col': placement === 'left' || placement === 'right' }]"
		>
			<!--			<XButton v-if="showScrollButtons.prev" class="!hfull !bg-border" @click="scrollPrev">
				{{ placement === 'top' || placement === 'bottom' ? '←' : '↑' }}
			</XButton>-->
			<div ref="navContainer" class="hfull flex-1 overflow-hidden scroll-smooth">
				<!--tab-->
				<div
					ref="nav"
					class="relative z-1 flex transition-all duration-300 ease-in-out"
					:class="[{ 'flex-row': placement === 'top' || placement === 'bottom', 'flex-col': placement === 'left' || placement === 'right' }]"
				>
					<div
						v-for="tab in tabs"
						:key="getTabValue(tab)"
						ref="tabItems"
						class="shrink-0 cursor-pointer whitespace-nowrap transition"
						@click="selectTab(tab)"
					>
						<XPopup :trigger="contextmenu ? 'contextmenu' : null" placement="bottom-end">
							<template v-if="contextmenu" #content="{ close }">
								<div class="flex flex-col bg-white p-xxs-xs" @click="close">
									<p class="cursor-pointer transition p-xxs-xs hover:bg-hover" @click="handleCloseCurrent(tab)">关闭当前</p>
									<p class="cursor-pointer transition p-xxs-xs hover:bg-hover" @click="handleCloseOthers(tab)">关闭其它</p>
									<p class="cursor-pointer transition p-xxs-xs hover:bg-hover" @click="handleCloseRight(tab)">关闭右侧</p>
									<p class="cursor-pointer transition p-xxs-xs hover:bg-hover" @click="handleCloseLeft(tab)">关闭左侧</p>
									<p class="cursor-pointer transition p-xxs-xs hover:bg-hover" @click="handleRefresh(tab)">刷新页面</p>
									<!-- 其它功能 -->
								</div>
							</template>
							<div
								class="relative flex items-center px-2xl py-xs"
								:style="{ background: !isLine && isTabActive(tab) ? activeBg : '' }"
								:class="[
									{
										'text-primary font-medium': isTabActive(tab),
										'arc-tab': isArcTab,
										'arc-tab-active': isArcTab && isTabActive(tab),
									},
									tabClass,
								]"
							>
								<div v-if="cardLine" v-show="isTabActive(tab)" class="absolute left-0 hfull w4px bg-primary"></div>
								<div :class="labelClass">{{ getTabLabel(tab) }}</div>
								<XIconsCloseLine
									v-if="closeable"
									class="cursor-pointer transition-all ml-xs hover:(rotate-360) !duration-500 !text-xxs"
									@click.stop="handleCloseCurrent(tab)"
								/>
							</div>
						</XPopup>
					</div>
					<div
						v-if="isLine"
						class="absolute bg-primary transition"
						:class="[isHorizontal ? 'h-2px bottom-0' : 'w-2px top-0', { 'left-0': placement === 'right', 'right-0': placement === 'right' }]"
						:style="lineStyle"
					></div>
				</div>
			</div>
			<!--			<XButton v-if="showScrollButtons.next" class="!hfull !bg-border" @click="scrollNext">
				{{ placement === 'top' || placement === 'bottom' ? '→' : '↓' }}
			</XButton>-->
		</div>
		<div class="flex-1">
			<slot></slot>
		</div>
	</div>
</template>

<script setup>
const props = defineProps({
	data: {
		type: Array,
		default: () => [],
	},
	activeBg: {
		type: String,
		default: themeColors.page,
	},
	placement: {
		type: String,
		default: 'top',
		validator: (v) => ['top', 'bottom', 'left', 'right'].includes(v),
	},
	cardLine: {
		type: Boolean,
		default: false,
	},
	type: {
		type: String,
		default: 'line',
		validator: (v) => ['line', 'card'].includes(v),
	},
	lazy: {
		type: Boolean,
		default: true,
	},
	modelValue: {
		type: [String, Number],
		default: '',
	},
	arcTabRadius: {
		type: String,
		default: '0.45rem',
	},
	arcTab: {
		type: Boolean,
		default: false,
	},
	closeable: {
		type: Boolean,
		default: false,
	},
	tabClass: {
		type: String,
		default: '',
	},
	labelClass: {
		type: String,
		default: '',
	},
	contextmenu: {
		type: Boolean,
		default: false,
	},
	labelKey: {
		type: String,
		default: 'label',
	},
	valueKey: {
		type: String,
		default: 'value',
	},
})
xThemeSetCssVar('--menu-tab-active-color', props.activeBg)
xThemeSetCssVar('--menu-tab-radius', props.arcTabRadius)
// Helper methods for tab value and label access
const getTabValue = (tab) => {
	if (!tab) return null
	return tab[props.valueKey] ?? null
}

const getTabLabel = (tab) => {
	if (!tab) return ''
	return tab[props.labelKey] ?? ''
}

const isTabActive = (tab) => {
	return getTabValue(tab) === currentValue.value
}

const findTabIndex = (tab) => {
	return tabs.value.findIndex((t) => getTabValue(t) === getTabValue(tab))
}

const emit = defineEmits(['update:modelValue', 'close', 'change'])
const navContainer = ref(null)
const nav = ref(null)
const tabItems = ref([])
const tabs = ref(props.data)
const currentValue = ref(props.modelValue)
const lineStyle = ref({})
const showScrollButtons = ref({ prev: false, next: false })
const isLine = props.type === 'line'

const isHorizontal = computed(() => ['top', 'bottom'].includes(props.placement))
const isArcTab = props.arcTab && props.type === 'card' && props.placement === 'top'
// 修改 provide 的方式，确保响应性
const tabsContext = shallowReactive({
	currentValue,
	lazy: props.lazy,
	registerTab: (tab) => {
		if (!tabs.value.find((t) => getTabValue(t) === getTabValue(tab))) {
			tabs.value.push(tab)
			if (!currentValue.value) {
				currentValue.value = getTabValue(tab)
				emit('update:modelValue', getTabValue(tab))
			}
		}
	},
	unregisterTab: (tab) => {
		const index = findTabIndex(tab)
		if (index !== -1) {
			tabs.value.splice(index, 1)
		}
	},
})

provide('tabs', tabsContext)

watch(
	() => props.modelValue,
	(val) => {
		currentValue.value = val
	},
)
watch(currentValue, async () => {
	await nextTick()
	updateLine()
	scrollToActiveTab()
	checkScrollButtons()
})

const selectTab = (tab) => {
	currentValue.value = getTabValue(tab)
	emit('update:modelValue', getTabValue(tab), tab)
	emit('change', tab)
}

const updateLine = () => {
	if (!isLine) return

	const activeIndex = tabs.value.findIndex((tab) => getTabValue(tab) === currentValue.value)
	if (activeIndex === -1 || !tabItems.value[activeIndex]) return

	const activeTab = tabItems.value[activeIndex]

	if (isHorizontal.value) {
		lineStyle.value = {
			left: `${activeTab.offsetLeft}px`,
			width: `${activeTab.offsetWidth}px`,
			height: '2px',
			top: 'auto',
			transition: 'all 0.3s',
		}
	} else {
		lineStyle.value = {
			top: `${activeTab.offsetTop}px`,
			height: `${activeTab.offsetHeight}px`,
			width: '2px',
			left: props.placement === 'left' ? 'auto' : '0',
			right: props.placement === 'left' ? '0' : 'auto',
			transition: 'all 0.3s',
		}
	}
}

const scrollToActiveTab = () => {
	const activeIndex = tabs.value.findIndex((tab) => getTabValue(tab) === currentValue.value)
	if (activeIndex === -1 || !tabItems.value[activeIndex]) return

	const container = navContainer.value
	const tab = tabItems.value[activeIndex]

	if (isHorizontal.value) {
		const containerWidth = container.clientWidth
		const tabLeft = tab.offsetLeft
		const tabWidth = tab.offsetWidth
		const maxScroll = container.scrollWidth - containerWidth

		// 计算目标滚动位置，使标签居中
		const targetScroll = Math.max(0, Math.min(tabLeft - (containerWidth - tabWidth) / 2, maxScroll))

		// 使用平滑滚动到目标位置
		container.scrollTo({
			left: targetScroll,
			behavior: 'smooth',
		})
	} else {
		const containerHeight = container.clientHeight
		const tabTop = tab.offsetTop
		const tabHeight = tab.offsetHeight
		const maxScroll = container.scrollHeight - containerHeight

		// 计算目标滚动位置，使标签居中
		const targetScroll = Math.max(0, Math.min(tabTop - (containerHeight - tabHeight) / 2, maxScroll))

		// 使用平滑滚动到目标位置
		container.scrollTo({
			top: targetScroll,
			behavior: 'smooth',
		})
	}
}

const handleWheel = (e) => {
	if (!navContainer.value) return

	const container = navContainer.value
	const delta = e.deltaY || e.deltaX
	const scrollAmount = delta * 0.6 // 直接使用实际的滚动距离，乘以一个系数来调整灵敏度

	if (isHorizontal.value) {
		// 检查是否可以滚动，只有在可以滚动的情况下才阻止默认行为
		const canScrollLeft = container.scrollLeft > 0
		const canScrollRight = container.scrollLeft + container.clientWidth < container.scrollWidth

		// 如果容器可以水平滚动且鼠标在容器区域内，则接管滚动
		if (canScrollLeft || canScrollRight) {
			if ((delta > 0 && canScrollRight) || (delta < 0 && canScrollLeft)) {
				e.preventDefault()
				container.scrollBy({
					left: scrollAmount,
					behavior: 'auto',
				})
			}
		}
	} else {
		// 检查是否可以滚动，只有在可以滚动的情况下才阻止默认行为
		const canScrollUp = container.scrollTop > 0
		const canScrollDown = container.scrollTop + container.clientHeight < container.scrollHeight

		// 如果容器可以垂直滚动且鼠标在容器区域内，则接管滚动
		if (canScrollUp || canScrollDown) {
			if ((delta > 0 && canScrollDown) || (delta < 0 && canScrollUp)) {
				e.preventDefault()
				container.scrollBy({
					top: scrollAmount,
					behavior: 'auto',
				})
			}
		}
	}
}

const scrollPrev = () => {
	if (isHorizontal.value) {
		navContainer.value.scrollBy({
			left: -100,
			behavior: 'smooth', // 按钮点击保持平滑效果
		})
	} else {
		navContainer.value.scrollBy({
			top: -100,
			behavior: 'smooth', // 按钮点击保持平滑效果
		})
	}
}

const scrollNext = () => {
	if (isHorizontal.value) {
		navContainer.value.scrollBy({
			left: 100,
			behavior: 'smooth', // 按钮点击保持平滑效果
		})
	} else {
		navContainer.value.scrollBy({
			top: 100,
			behavior: 'smooth', // 按钮点击保持平滑效果
		})
	}
}

const checkScrollButtons = () => {
	if (!navContainer.value) return

	if (isHorizontal.value) {
		showScrollButtons.value.prev = navContainer.value.scrollLeft > 0
		showScrollButtons.value.next = navContainer.value.scrollLeft + navContainer.value.clientWidth < navContainer.value.scrollWidth
	} else {
		showScrollButtons.value.prev = navContainer.value.scrollTop > 0
		showScrollButtons.value.next = navContainer.value.scrollTop + navContainer.value.clientHeight < navContainer.value.scrollHeight
	}
}

// 关闭当前标签
const handleCloseCurrent = async (tab) => {
	const closeIndex = findTabIndex(tab)
	if (closeIndex === -1) return
	// 触发关闭事件
	emit('close', tab, closeIndex)
	await nextTick()

	// 如果关闭的是当前标签
	if (isTabActive(tab)) {
		// 优先找右侧标签，否则找左侧
		const newTab = tabs.value[closeIndex + 1] || tabs.value[closeIndex - 1]
		if (newTab) {
			selectTab(newTab)
		}
	}

	// 从数据源移除
	tabs.value.splice(closeIndex, 1)
}

// 关闭其他标签（使用splice保持响应性）
const handleCloseOthers = (currentTab) => {
	const keptTabs = tabs.value.filter((t) => getTabValue(t) === getTabValue(currentTab) || t.fixed)

	// 使用splice清空并替换内容
	tabs.value.splice(0, tabs.value.length, ...keptTabs)

	// 检查当前活动标签
	if (!tabs.value.some((t) => getTabValue(t) === currentValue.value)) {
		nextTick(() => selectTab(tabs.value[0]))
	}
}

// 关闭右侧标签（使用splice）
const handleCloseRight = (currentTab) => {
	const currentIndex = findTabIndex(currentTab)
	if (currentIndex === -1) return

	// 计算需要保留的标签（当前及左侧+固定标签）
	const keepIndex = tabs.value.slice(currentIndex).findIndex((t) => t.fixed)

	const deleteCount = keepIndex === -1 ? tabs.value.length - currentIndex - 1 : keepIndex - 1

	if (deleteCount > 0) {
		tabs.value.splice(currentIndex + 1, deleteCount)
		nextTick(checkActiveTabExists)
	}
}

// 关闭左侧标签（使用splice）
const handleCloseLeft = (currentTab) => {
	const currentIndex = findTabIndex(currentTab)
	if (currentIndex === -1) return

	// 找到第一个不能删除的左侧标签
	const firstFixedIndex = tabs.value.slice(0, currentIndex).findLastIndex((t) => t.fixed)

	const deleteCount = firstFixedIndex === -1 ? currentIndex : currentIndex - firstFixedIndex - 1

	if (deleteCount > 0) {
		tabs.value.splice(firstFixedIndex + 1, deleteCount)
		nextTick(checkActiveTabExists)
	}
}

// 刷新当前页
const handleRefresh = (tab) => {
	emit('refresh', tab)
}

// 检查当前活动标签是否存在
const checkActiveTabExists = () => {
	if (!tabs.value.some((t) => getTabValue(t) === currentValue.value)) {
		selectTab(tabs.value[tabs.value.length - 1])
	}
}

onMounted(() => {
	window.addEventListener('resize', checkScrollButtons)
	navContainer.value?.addEventListener('scroll', checkScrollButtons)
	// 添加wheel事件监听器，使用passive: false以便在需要时调用preventDefault
	navContainer.value?.addEventListener('wheel', handleWheel, { passive: false })
	nextTick(() => {
		updateLine()
		checkScrollButtons()
	})
})

onBeforeUnmount(() => {
	window.removeEventListener('resize', checkScrollButtons)
	navContainer.value?.removeEventListener('scroll', checkScrollButtons)
	// 移除wheel事件监听器
	navContainer.value?.removeEventListener('wheel', handleWheel)
})
</script>

<style scoped lang="less">
/*
borderRadius: `${radius} ${radius} 0 0`
*/
.arc-tab {
	border-radius: var(--menu-tab-radius) var(--menu-tab-radius) 0 0;
}
/*
 after:(absolute bottom-0 content-['']) before:(absolute bottom-0 content-[''])
*/
/*
after:(w-[var(--menu-tab-radius)] h-[var(--menu-tab-radius)]) before:(w-[var(--menu-tab-radius)] h-[var(--menu-tab-radius)])
*/
.arc-tab::after,
.arc-tab::before {
	content: '';
	position: absolute;
	bottom: 0;
	width: var(--menu-tab-radius);
	height: var(--menu-tab-radius);
}

.arc-tab ::after,
.arc-tab ::before {
}

/*
before:(-left-[var(--menu-tab-radius)] bg-[radial-gradient(circle_at_0_0,transparent_var(--menu-tab-radius),var(--menu-tab-active-color)_var(--menu-tab-radius))]) after:(-right-[var(--menu-tab-radius)] bg-[radial-gradient(circle_at_100%_0,transparent_var(--menu-tab-radius),var(--menu-tab-active-color)_var(--menu-tab-radius))])*/
.arc-tab-active::before {
	left: calc(0px - var(--menu-tab-radius));
	background: radial-gradient(circle at 0 0, transparent var(--menu-tab-radius), var(--menu-tab-active-color) var(--menu-tab-radius));
}
.arc-tab-active::after {
	right: calc(0px - var(--menu-tab-radius));
	background: radial-gradient(circle at 100% 0, transparent var(--menu-tab-radius), var(--menu-tab-active-color) var(--menu-tab-radius));
}
.arc-tab-active {
	background: var(--menu-tab-active-color);
}
</style>
