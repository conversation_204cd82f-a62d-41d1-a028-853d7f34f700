import { defineStore } from 'pinia'
export default defineStore('biz-user', {
	state() {
		return {
			platform: 'web',
			token: '',
			userinfo: {
				id: null,
				avatar: '',
				nickname: '',
				realName: '',
				catePath: '',
				createTime: '',
				deptId: null,
			},
			permissions: [],
		}
	},
	actions: {
		goMy() {
			goMy()
		},
		logout(isGoLogin = false) {
			const platform = this.platform
			if (isGoLogin) {
				goLogin()
			}
			BIZ_Auth_APIS.logout()
			/*			const alertStore = useAlertStore()
			alertStore.$reset()
			const transportOrderStore = useTransportOrderStore()
			transportOrderStore.$reset()
			const plantStore = usePlantStore()
			plantStore.$reset()*/

			this.$reset()
			this.platform = platform
		},
		setUserinfo(payload) {
			console.log('use----', payload)
			this.userinfo = payload
			// if (!this.userinfo.avatar?.includes('http')) {
			// 	this.userinfo.avatar = 'https://api.dicebear.com/7.x/avataaars/svg?seed=Felix'
			// }
			// Object.assign(this.userinfo, payload || initilUserinfo)
		},
	},
	getters: {
		getUserinfo() {
			return this.userinfo
		},
		isPlatformSuper() {
			return this.userinfo?.deptId == 2
		},
	},
	persist: true,
})
