<template>
	<div class="flex flex-wrap items-center gap-4">
		<span class="text-secondary text-sm">{{ total }}条</span>
		<!-- 每页条数选择 -->
		<XSelect
			v-model="pageSize"
			class="!h-1.8rem !w6rem"
			:clearable="false"
			:options="pageSizes.map((item) => ({ name: item + '条/页', id: item }))"
			@change="handlePageSizeChange"
		/>
		<!-- 分页主体 -->
		<div class="flex items-center gap-xs">
			<!-- 上一页 -->
			<XButton
				class="h1.8rem w1.8rem !bg-transparent !text-content !border-border"
				:disabled="currentPage === 1 || totalPages === 0"
				@click="handlePageChange('prev')"
			>
				<div class="i-bi:chevron-left shrink-0"></div>
			</XButton>
			<!-- 页码列表 -->
			<template v-for="(item, index) in pageNumbers" :key="index">
				<div
					v-if="item.type === 'number'"
					class="h1.8rem w1.8rem flex cursor-pointer select-none items-center justify-center rounded-50% text-xs border-border"
					:class="{ 'bg-primary text-white': item.value === currentPage, 'hover:bg-tip': item.value !== currentPage }"
					@click="handlePageChange('pageNumber', item.value)"
				>
					{{ item.value }}
				</div>
				<div
					v-else
					class="h1.8rem w1.8rem flex cursor-pointer select-none items-center justify-center rounded-50% hover:bg-tip"
					@click="handleEllipsisClick(item)"
				>
					...
				</div>
			</template>

			<!-- 下一页 -->
			<XButton
				class="h1.8rem w1.8rem !bg-transparent !text-content !border-border"
				:disabled="currentPage === totalPages || totalPages === 0"
				@click="handlePageChange('next')"
			>
				<div class="i-bi:chevron-left shrink-0 rotate-180"></div>
			</XButton>
		</div>

		<!-- 跳转 -->
		<div class="flex items-center gap-2">
			<XInput v-model.number="jumpPage" type="number" min="1" :max="totalPages" class="text-sm !h-1.8rem !w-5rem" @keyup.enter="handleJump" />
			<XButton class="" @click="handleJump">跳转</XButton>
		</div>
	</div>
</template>

<script setup>
const props = defineProps({
	total: { type: Number, required: true },
	pageSizes: { type: Array, default: () => [10, 20, 50, 100] },
})
const emit = defineEmits(['change', 'page-size-change'])
const currentPage = defineModel({ type: Number, default: 1 })
const pageSize = defineModel('page-size', { type: Number, default: 10 })
const totalPages = computed(() => Math.ceil(props.total / props.pageSize))
const jumpPage = ref('')

// 分页配置项
const pageConfig = {
	nearCurrentCount: 1, // 当前页左右各显示多少页
	startActiveThreshold: 3, // 当前页小于等于该值时，视为在起始区域
	endActiveThreshold: 2, // 当前页距离尾页小于等于该值时，视为在尾部区域
	maxContinuousPages: 3, // 最大连续显示页码数（不包括首尾页）
}

// 页码项接口定义
/**
 * @typedef {Object} PageItem
 * @property {'number' | 'ellipsis'} type - 项类型：数字或省略号
 * @property {number} value - 页码值
 * @property {number} [prevValue] - 省略号前一个页码
 * @property {number} [nextValue] - 省略号后一个页码
 */

// 生成页码列表
const pageNumbers = computed(() => {
	/** @type {PageItem[]} */
	const pages = []
	const current = currentPage.value
	const total = totalPages.value
	const { nearCurrentCount, startActiveThreshold, endActiveThreshold, maxContinuousPages } = pageConfig

	// 始终显示第一页
	pages.push({ type: 'number', value: 1 })

	// 计算中间省略号
	if (current > startActiveThreshold) {
		pages.push({
			type: 'ellipsis',
			value: 0,
			prevValue: 1,
			nextValue: Math.min(current - 1, total - maxContinuousPages),
		})
	}

	// 计算当前页附近的页码
	let start = Math.max(2, current - nearCurrentCount)
	let end = Math.min(total - 1, current + nearCurrentCount)

	// 调整start和end以确保显示足够的页码
	if (current <= startActiveThreshold) {
		start = 2
		end = Math.min(maxContinuousPages + 1, total - 1)
	} else if (current >= total - endActiveThreshold) {
		start = Math.max(total - maxContinuousPages, 2)
		end = total - 1
	}

	// 添加页码
	for (let i = start; i <= end; i++) {
		pages.push({ type: 'number', value: i })
	}

	// 添加末尾省略号
	if (current < total - endActiveThreshold) {
		pages.push({
			type: 'ellipsis',
			value: 0,
			prevValue: Math.max(current + 1, total - maxContinuousPages + 1),
			nextValue: total,
		})
	}

	// 始终显示最后一页
	if (total > 1) {
		pages.push({ type: 'number', value: total })
	}

	return pages
})

/**
 *
 * 处理页码变更
 * @param {'prev' | 'next' | 'pageNumber'} type - 操作类型
 * @param {number} [page] - 目标页码，当type为pageNumber时使用
 */
function handlePageChange(type, page) {
	let newPage = currentPage.value

	if (type === 'prev' && currentPage.value > 1) {
		newPage = currentPage.value - 1
	} else if (type === 'next' && currentPage.value < totalPages.value) {
		newPage = currentPage.value + 1
	} else if (type === 'pageNumber' && page) {
		newPage = page
	}

	currentPage.value = newPage
	emit('change', newPage)
}

/**
 * 处理省略号点击事件
 * @param {PageItem} item - 省略号项
 */
function handleEllipsisClick(item) {
	if (item.type === 'ellipsis' && item.prevValue && item.nextValue) {
		const middlePage = Math.floor((item.prevValue + item.nextValue) / 2)
		handlePageChange('pageNumber', middlePage)
	}
}

function handleJump() {
	if (!jumpPage.value || jumpPage.value < 1 || jumpPage.value > totalPages.value) return
	handlePageChange('pageNumber', Number(jumpPage.value))
	jumpPage.value = ''
}

watch(totalPages, (newVal) => {
	if (currentPage.value > newVal) {
		currentPage.value = newVal || 1
	}
})

function handlePageSizeChange() {
	currentPage.value = 1
	emit('change')
}
</script>

<style></style>
