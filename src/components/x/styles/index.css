@import url('./variables.css');
html{
    font-size:16px;
    line-height: 1;
	/*	设置字体  */
}
body{
	background: linear-gradient(135deg, #1a2b4d 0%, #101b3d 100%);
	margin: 0;
	padding: 0;
	font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

#nprogress {
    pointer-events: none;
}

#nprogress .bar {
    background: repeating-linear-gradient(90deg, #00dc82 0, #34cdfe 50%, #0047e1);
    position: fixed;
    z-index: 1031;
    top: 0;
    left: 0;

    width: 100%;
    height: 2px;
}


/*
* 全局滚动条
*/
::-webkit-scrollbar {
	width: 4px;
	height:4px;
}

::-webkit-scrollbar-thumb {
	background-color: #dddddd;
	border-radius: 10px;
}

::-webkit-scrollbar-track {
	background-color: rgba(0,0,0,0.05);
}


/*
横向滚动条
*/
