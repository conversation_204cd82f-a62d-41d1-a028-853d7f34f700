<script setup lang="ts">
const props = defineProps({
	text: {
		type: String,
		default: '',
	},
	loading: {
		type: Boolean,
		default: false,
	},
	disabled: {
		type: Boolean,
		default: false,
	},
	disabledTip: {
		type: String,
		default: null,
	},
	textMode: {
		type: <PERSON>olean,
		default: false,
	},
	type: {
		type: String,
		default: 'button',
	},
})
const emit = defineEmits(['click'])
function clickHandler(event) {
	if (!props.loading && !props.disabled) {
		// 提交form的submit
		if (props.type === 'submit') {
			const button = event.currentTarget // 获取触发事件的按钮元素
			const form = button.closest('form') // 查找最近的父级 form
			if (form) {
				form.dispatchEvent(new Event('submit', { cancelable: true }))
			}
		}
		return emit('click', event)
	}
	if (props.disabled && props.disabledTip) {
		toast.warning(props.disabledTip)
	}
}

const debounceClickHandler = xUseDebounce(clickHand<PERSON>, 20, true)
</script>

<template>
	<button
		v-loading="loading"
		:type="type"
		:disabled="disabled || loading"
		class="flex cursor-pointer select-none items-center justify-center overflow-hidden truncate rounded-md text-nowrap text-sm disabled:cursor-not-allowed active:(opacity-50) hover:(ring-2 ring-primaryLight transition)"
		:class="[disabled && 'opacity-50', { 'px-lg h-2rem bg-primary text-white': !textMode }, { 'text-primary': textMode }]"
		@click.prevent="debounceClickHandler"
	>
		<span>{{ text }}</span>
		<slot></slot>
	</button>
</template>

<style>
button {
	background: transparent;
}
</style>
