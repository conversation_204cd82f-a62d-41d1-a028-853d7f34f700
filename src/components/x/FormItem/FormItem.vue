<template>
	<div>
		<div class="flex" :class="[{ 'flex-col': computedLabelPosition === 'top', 'items-center': computedLabelPosition === 'left' }]">
			<XLabel
				v-if="label"
				:style="{ width: computedLabelWidth, minWidth: computedLabelWidth }"
				:class="[{ 'mb-xxs': computedLabelPosition === 'top' }]"
				class="block flex text-sm"
				:title="label"
			>
				<span class="truncate">{{ label }}</span>
				<span v-if="isRequired" class="text-danger-300">*</span>
			</XLabel>
			<!--flex-1溢出问题-->
			<div class="min-w-0 flex-1" :style="{ width: itemWidth || formContext.itemWidth || '100%' }">
				<slot :error="error" :status="status"></slot>
			</div>
		</div>
		<div
			v-if="errorVisible && formContext.errorVisible"
			class="text-danger-300 text-xs h-xs"
			:style="{ paddingLeft: computedLabelPosition === 'left' ? computedLabelWidth : 0 }"
		>
			{{ error }}
		</div>
	</div>
</template>

<script setup lang="ts">
import Schema from 'async-validator'

const props = defineProps({
	label: {
		type: String,
		default: '',
	},
	prop: {
		type: String,
		default: null,
	},
	labelWidth: {
		type: String,
	},
	itemWidth: {
		type: String,
		default: '100%',
	},
	errorVisible: {
		type: Boolean,
		default: true,
	},
	required: {
		type: Boolean,
		default: false,
	},
	labelPosition: {
		type: String,
	},
})

// 将 prop 提供给子组件
const error = ref('')
const status = ref('') // 'success' | 'error' | 'validating'
provide('formItemContext', { prop: props.prop, error, status })
const formContext = inject('formContext', {
	labelPosition: 'left',
	rules: {},
	model: {},
	addItem: () => {},
	removeItem: () => {},
	clearError: () => {},
	getFieldValue: () => {},
})
const computedLabelWidth = computed(() => {
	return props.labelWidth || formContext.labelWidth || '4.5rem'
})
// 注册到Form组件
onMounted(() => {
	if (props.prop) {
		formContext.addItem({ validate, clearError, prop: props.prop })
	}
})

// 组件卸载时移除表单项
onUnmounted(() => {
	if (props.prop) {
		formContext.removeItem(props.prop)
	}
})

const computedLabelPosition = computed(() => {
	if (props.labelPosition) return props.labelPosition
	return formContext.labelPosition
})

// 必填标识
const isRequired = computed(() => {
	if (props.required) return true
	const rules = formContext.rules[props.prop]
	if (!rules) return false
	return [].concat(rules).some((rule) => rule.required)
})

const validate = async (trigger) => {
	if (!props.prop) return
	const rules = formContext.rules[props.prop] || []
	// 过滤出符合当前 trigger 的规则
	let filteredRules = []
	for (let rule of rules) {
		if (!trigger) {
			filteredRules.push(rule)
		} else {
			if (!rule.trigger && rule.required) {
				filteredRules.push({ required: true, message: `${props.label}不能为空` })
			}
			if (rule.trigger) {
				if (rule.trigger?.includes(trigger)) {
					filteredRules.push(rule)
				}
			}
		}
	}
	if (filteredRules.length === 0) return { valid: true }
	status.value = 'validating'

	try {
		// 创建验证规则时，需要移除trigger属性，因为async-validator不识别这个属性
		const validationRules = filteredRules.map((rule) => {
			const { trigger, ...validationRule } = rule
			return validationRule
		})
		const validator = new Schema({ [props.prop]: validationRules })
		const value = formContext.getFieldValue(props.prop)
		await validator.validate({ [props.prop]: value }, { first: true })
		error.value = ''
		status.value = 'success'
		return { valid: true }
	} catch (err) {
		error.value = err.errors[0].message
		status.value = 'error'
		return { valid: false, errors: err.errors }
	}
}

// 清除错误
const clearError = () => {
	error.value = ''
	status.value = ''
}

defineExpose({ validate, clearError, prop: props.prop })
</script>

<style scoped></style>
