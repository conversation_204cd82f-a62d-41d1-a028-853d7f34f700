<template>
	<div
		class="table-container overflow-x-auto text-sm"
		:style="{
			height: props.height ? props.height + 'px' : 'auto',
			maxHeight: props.maxHeight && !props.height ? (typeof props.maxHeight === 'number' ? props.maxHeight + 'px' : props.maxHeight) : 'none',
			background: 'var(--color-tableBg)',
		}"
	>
		<!-- 拖拽指示线 -->
		<div v-if="isDragging" class="resize-indicator" :style="{ left: `${resizeIndicatorPosition}px` }"></div>
		<div ref="tableWrapper" class="table-wrapper">
			<!-- 固定表头 -->
			<div class="table-header-wrapper" style="background: var(--color-tableHeaderBg)">
				<table class="tree-table table-header wfull border-collapse" style="table-layout: fixed">
					<colgroup :key="`header-${updateKey}`">
						<col v-if="selectable" :style="{ width: selectColWidth }" />
						<col v-for="(col, index) in flattenedColumns" :key="`col-${index}-${updateKey}`" :style="{ width: `${col.width}px` }" />
					</colgroup>
					<thead>
						<template v-for="(row, rowIndex) in headerRows" :key="`header-row-${rowIndex}`">
							<tr>
								<!-- 复选框列 -->
								<th
									v-if="rowIndex === 0 && selectable"
									class="relative color-secondary border-border"
									style="background: var(--color-tableHeaderBg); color: var(--color-tableHeaderText)"
									:class="[headerClass, { 'fixed-left': true, 'last-fixed-left': !hasFixedLeft }]"
									:rowspan="headerRows.length"
									:style="[getFixedStyle({ fixed: 'left' }, 0), { textAlign: 'center' }]"
								>
									<input type="checkbox" :checked="allSelected" :indeterminate="hasIndeterminate" @change="toggleAllSelection" />
								</th>
								<template v-for="(col, colIndex) in row" :key="`header-${rowIndex}-${colIndex}`">
									<th
										v-if="col"
										class="relative color-secondary border-border"
										:style="[
											getFixedStyle(col, colIndex),
											{ textAlign: headerAlign, background: 'var(--color-tableHeaderBg)', color: 'var(--color-tableHeaderText)' },
										]"
										:class="[
											headerClass,
											{
												'fixed-left': col.fixed === 'left',
												'last-fixed-left': isLastFixedLeft(col),
												'fixed-right': col.fixed === 'right',
												'first-fixed-right': isFirstFixedRight(col),
											},
										]"
										:colspan="col.colspan"
										:rowspan="col.rowspan"
										:data-col="col[colValueKey]"
										:title="col.label"
									>
										{{ col.label }}
										<div class="resize-handle" @mousedown.stop="onDragStart($event, colIndex, col)"></div>
									</th>
								</template>
							</tr>
						</template>
					</thead>
				</table>
				<!-- 滚动条宽度补偿，只在需要时显示 -->
				<div v-if="showScrollbar" class="scrollbar-compensation" :style="{ width: scrollbarWidth + 'px' }"></div>
			</div>

			<!-- 表格内容区域 -->
			<div
				ref="tableContainer"
				class="table-body-container"
				style="--x-table-text: var(--color-tableText); --x-table-row-hover: var(--color-tableRowHoverBg)"
				@scroll="onScroll"
			>
				<table class="tree-table table-body wfull border-collapse" style="table-layout: fixed">
					<colgroup :key="`body-${updateKey}`">
						<col v-if="selectable" :style="{ width: selectColWidth }" />
						<col v-for="(col, index) in flattenedColumns" :key="`col-${index}-${updateKey}`" :style="{ width: `${col.width}px` }" />
					</colgroup>
					<tbody>
						<template v-for="(row, rowIndex) in visibleRows" :key="row[valueKey] || rowIndex">
							<tr :class="`level-${row.level}`">
								<!-- 复选框列 -->
								<td
									v-if="selectable"
									class="select-cell text-center border-border"
									:class="{ 'fixed-left': true, 'last-fixed-left': !hasFixedLeft }"
									:style="getFixedStyle({ fixed: 'left' }, 0)"
								>
									<input type="checkbox" :checked="isRowSelected(row)" :indeterminate="isRowIndeterminate(row)" @change="toggleRowSelection(row)" />
								</td>

								<!-- 数据列 -->
								<td
									v-for="(col, colIndex) in flattenedColumns"
									:key="colIndex"
									class="text-sm border-border"
									:class="[
										colClass,
										ellipsis ? 'truncate' : 'whitespace-pre-wrap break-all',
										formatColumnValue(row, col)?.styleClasses,
										{
											'fixed-left': col.fixed === 'left',
											'last-fixed-left': isLastFixedLeft(col),
											'fixed-right': col.fixed === 'right',
											'first-fixed-right': isFirstFixedRight(col),
										},
									]"
									:style="[
										{ width: `${col.width}px` },
										getFixedStyle(col, colIndex),
										{ textAlign: treeable && colIndex === 0 ? 'left' : bodyAlign, color: 'var(--color-tableText)' },
									]"
									:title="formatColumnValue(row, col)?.content"
								>
									<template v-if="colIndex === 0 && treeable">
										<div class="flex items-center" :style="{ paddingLeft: `${row.level * indentSize}px` }">
											<div class="relative min-w-1rem flex items-center">
												<span v-if="hasChildren(row)">
													<div class="" @click="toggleExpand(row)">
														<span v-if="row.loading" class="loading-icon">...</span>
														<span v-else-if="row.loadError" class="error-icon">!</span>
														<XButton v-else text-mode>
															<XIconsChevronRight :class="[row.isExpanded && 'rotate-90']" class="transition" />
														</XButton>
													</div>
												</span>
												<span v-else></span>
											</div>
											<span class="ml-2">
												<span v-if="!$slots?.[col?.[colValueKey]]">
													<template v-if="formatColumnValue(row, col).type === 'render'">
														<component :is="formatColumnValue(row, col).render" />
													</template>
													<template v-else-if="formatColumnValue(row, col).type === 'component'">
														<component
															:is="formatColumnValue(row, col).config.component"
															v-bind="formatColumnValue(row, col).config.props"
															:style="formatColumnValue(row, col).config.style"
															v-on="formatColumnValue(row, col).config.events"
														/>
													</template>
													<template v-else-if="formatColumnValue(row, col).type === 'html'">
														<div class="html-content-wrapper" :class="{ 'html-ellipsis': formatColumnValue(row, col).ellipsis }">
															<span v-html="formatColumnValue(row, col).content"></span>
														</div>
													</template>
													<template v-else>
														{{ formatColumnValue(row, col).content }}
													</template>
												</span>
												<slot
													:name="col?.[colValueKey]"
													:row="row"
													:row-index="rowIndex"
													:tag-label="formatColumnValue(row, col).tagLabel"
													:raw-value="formatColumnValue(row, col).rawValue"
													:style-classes="formatColumnValue(row, col).styleClasses"
												></slot>
											</span>
										</div>
									</template>
									<template v-else>
										<span v-if="!$slots?.[col?.[colValueKey]]">
											<template v-if="formatColumnValue(row, col).type === 'render'">
												<component :is="formatColumnValue(row, col).render" />
											</template>
											<template v-else-if="formatColumnValue(row, col).type === 'component'">
												<component
													:is="formatColumnValue(row, col).config.component"
													v-bind="formatColumnValue(row, col).config.props"
													:style="formatColumnValue(row, col).config.style"
													v-on="formatColumnValue(row, col).config.events"
												/>
											</template>
											<template v-else-if="formatColumnValue(row, col).type === 'html'">
												<div class="html-content-wrapper" :class="{ 'html-ellipsis': formatColumnValue(row, col).ellipsis }">
													<span v-html="formatColumnValue(row, col).content"></span>
												</div>
											</template>
											<template v-else>
												{{ formatColumnValue(row, col).content }}
											</template>
										</span>
										<slot
											:name="col?.[colValueKey]"
											:row="row"
											:row-index="rowIndex"
											:col="col"
											:tag-label="formatColumnValue(row, col).tagLabel"
											:raw-value="formatColumnValue(row, col).rawValue"
											:style-classes="formatColumnValue(row, col).styleClasses"
										></slot>
									</template>
								</td>
							</tr>
						</template>
					</tbody>
				</table>
			</div>
		</div>
		<div class="absolute hfull wfull flex items-center justify-center">
			<XEmpty v-if="loading || (!loading && !(data?.length > 0))" :content="loading ? 'loading...' : '暂无数据'"></XEmpty>
		</div>
	</div>
</template>

<script setup>
import { useFixedColumns } from './composables/useFixedColumns'
import { useResizableColumns } from './composables/useResizableColumns'
import { useTreeTable } from './composables/useTreeTable'
import { useRowSelection } from './composables/useRowSelection'
import { useTableScroll } from './composables/useTableScroll'
import { useColumnProcessor } from './composables/useColumnProcessor'
import { useColumnFormatter } from './composables/useColumnFormatter'

// ================================
// Props 定义 - 组件属性配置
// ================================
const props = defineProps({
	// 基础数据配置
	data: { type: Array, default: () => [] },
	columns: { type: Array, required: true },
	loading: { type: Boolean, default: false },

	// 样式配置
	headerClass: { type: String, default: '' },
	colClass: { type: String, default: '' },
	ellipsis: { type: Boolean, default: false },
	headerAlign: { type: String, default: 'center' }, // 表头对齐方式：left/center/right
	bodyAlign: { type: String, default: 'center' }, // 表格内容对齐方式：left/center/right

	// 键值配置
	colValueKey: { type: String, default: 'prop' },
	valueKey: { type: String, default: 'id' },
	rowKey: { type: String, default: 'id' },

	// 功能开关
	selectable: { type: Boolean, default: false },
	treeable: { type: Boolean, default: false },

	// 树形表格配置
	defaultExpandAll: { type: Boolean, default: false },
	accordion: { type: Boolean, default: false },
	indentSize: { type: Number, default: 24 },
	checkStrictly: { type: Boolean, default: true },
	parentClickSelectsChildren: { type: Boolean, default: false },

	// 懒加载配置
	loadData: { type: Function, default: null },
	lazyLoadRetries: { type: Number, default: 3 },
	lazyLoadRetryDelay: { type: Number, default: 1000 },

	// 布局配置
	height: { type: Number, default: null },
	maxHeight: { type: [Number, String], default: '100%' },
	buffer: { type: Number, default: 10 },
	selectColWidth: { type: String, default: '2.5rem' },
})

const emit = defineEmits(['update:selected', 'expand-change', 'lazy-load-error', 'selection-change', 'node-delete', 'update:data'])

// DOM引用
const tableWrapper = ref(null)
const tableContainer = ref(null)

// ================================
// 使用组合式API组织功能模块
// ================================

// 处理列配置和多级表头
const { flattenedColumns, headerRows, flattenedColumnsMap } = useColumnProcessor(props)

// 处理列格式化功能
const { formatColumnValue, getTagLabel } = useColumnFormatter(props, flattenedColumns)

// 处理固定列逻辑
const { hasFixedLeft, isLastFixedLeft, isFirstFixedRight, getFixedStyle } = useFixedColumns(props, flattenedColumns, flattenedColumnsMap)

// 处理滚动和布局相关
const { showScrollbar, scrollbarWidth, onScroll, checkScrollbarVisibility, forceScrollUpdate } = useTableScroll(tableWrapper, tableContainer)

// 处理列宽调整功能
const { isDragging, resizeIndicatorPosition, onDragStart, updateKey } = useResizableColumns(props, flattenedColumns, flattenedColumnsMap, emit)

// 处理树形表格功能
const { nodeMap, treeData, visibleRows, hasChildren, toggleExpand, updateTreeData, deleteNode, saveNode } = useTreeTable(
	props,
	emit,
	checkScrollbarVisibility,
	forceScrollUpdate,
)

// 处理行选择功能
const {
	allSelected,
	hasIndeterminate,
	isRowSelected,
	isRowIndeterminate,
	toggleRowSelection,
	toggleAllSelection,
	setRowSelection,
	clearSelection,
	getSelectedRows,
} = useRowSelection(props, visibleRows, nodeMap, emit)

// ================================
// 数据处理和生命周期
// ================================

// 监听数据变化
watch(
	() => props.data,
	(newData) => {
		// 使用树形表格函数处理数据更新
		if (props.treeable) {
			treeData.value = updateTreeData(newData)
		}

		// 清空选择状态
		clearSelection()

		// 延迟更新布局
		nextTick(() => {
			checkScrollbarVisibility()
		})
	},
	{ immediate: true },
)

// 组件挂载初始化
onMounted(() => {
	nextTick(checkScrollbarVisibility)
	window.addEventListener('resize', checkScrollbarVisibility)
})

// 组件卸载清理
onBeforeUnmount(() => {
	window.removeEventListener('resize', checkScrollbarVisibility)
})

// ================================
// 工具函数模块
// ================================

// ================================
// 组件暴露接口
// ================================
defineExpose({
	setRowSelection,
	getSelectedRows,
	clearSelection,
	deleteNode,
	saveNode,
	getTagLabel,
})
</script>

<style scoped>
/* 表格容器样式 */
.table-container {
	position: relative;
	display: flex;
	flex-direction: column;
	/*	border: 1px solid #e5e7eb;*/
	overflow: hidden; /* 防止内容溢出 */
	min-height: 40px; /* 确保表格至少有一行的高度 */
	margin: 0 auto; /* 表格居中 */
}

.table-wrapper {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

/* 表头包装器样式 */
.table-header-wrapper {
	display: flex;
	width: 100%;
	background: #fff;
	z-index: 2;
	flex-shrink: 0; /* 防止表头被压缩 */
	overflow-x: hidden; /* 隐藏水平滚动条，由内容区域控制 */
	position: relative; /* 确保正确的堆叠上下文 */
}

/* 滚动条宽度补偿 */
.scrollbar-compensation {
	flex-shrink: 0;
	background-color: #f9fafb;
}

/* 固定表头样式 */
.table-header {
	position: sticky;
	top: 0;
	z-index: 2;
	background: var(--color-tableHeaderBg);
	table-layout: fixed; /* 确保列宽固定 */
	width: 100%;
	margin: 0;
	border-collapse: separate;
	border-spacing: 0;
	flex: 1;
}

/* 表格内容区域样式 */
.table-body-container {
	flex: 1;
	overflow-y: auto;
	overflow-x: auto; /* 启用水平滚动 */
	position: relative;
	width: 100%;
	min-height: 20px; /* 减小最小高度，让它更自然 */
}

.table-body {
	width: 100%;
	table-layout: fixed; /* 确保列宽固定 */
	border-collapse: separate;
	border-spacing: 0;
}

/* 调整表头和单元格的基础样式 */
.tree-table th,
.tree-table td {
	padding: 8px 12px;
	box-sizing: border-box;
	height: auto; /* 允许单元格高度自适应 */
	min-height: 40px; /* 设置最小高度 */
	overflow: hidden; /* 防止内容溢出 */
	text-overflow: ellipsis; /* 超出内容显示省略号 */
}

.tree-table th {
	font-weight: 500;
	background-color: var(--color-tableHeaderBg);
	color: var(--color-tableHeaderText);
	position: relative;
	white-space: nowrap; /* 防止换行 */
	padding-right: 16px; /* 确保文本不会与resize handle重叠 */
	border: none; /* 去掉所有边框 */
}

.tree-table td {
	border: none; /* 去掉表格body的边框 */
}

.resize-handle {
	position: absolute;
	top: 0;
	right: 0;
	width: 8px;
	height: 100%;
	cursor: col-resize;
	z-index: 3;
}

.resize-handle:hover {
	background-color: rgba(0, 0, 0, 0.1);
}

.resize-handle:active {
	background-color: rgba(64, 158, 255, 0.3);
}

.tree-table tr {
	transition: background-color 0.2s;
	height: auto; /* 允许行高自适应 */
	min-height: 40px; /* 确保行有最小高度 */
}

.tree-table tr:hover {
	background-color: var(--color-tableRowHoverBg);
}

/* 固定列样式 */
.fixed-left,
.fixed-right {
	position: sticky !important; /* 使用!important确保优先级 */
	z-index: 1;
}

/* 表头中的固定列样式 */
.table-header th.fixed-left,
.table-header th.fixed-right {
	background-color: var(--color-tableHeaderBg) !important; /* 确保与表头背景色一致 */
	z-index: 3 !important; /* 提高表头固定列的z-index，确保它们显示在其他内容之上 */
}

/* 表格内容中的固定列 */
.table-body td.fixed-left,
.table-body td.fixed-right {
	background-color: var(--color-tableFixedColBg) !important; /* 确保内容区域的固定列背景色 */
}

/* 最后一个左侧固定列的右边框阴影效果 */
.last-fixed-left {
	box-shadow: 6px 0 6px -6px rgba(0, 0, 0, 0.15);
}

/* 第一个右侧固定列的左边框阴影效果 */
.first-fixed-right {
	box-shadow: -6px 0 6px -6px rgba(0, 0, 0, 0.15);
}

/* 当行悬停时保持固定列的背景色与行悬停色一致 */
.tree-table tr:hover .fixed-left,
.tree-table tr:hover .fixed-right {
	background-color: rgba(0, 0, 0, 0.02);
}

/* 选择框样式 */
.select-cell input[type='checkbox'] {
	position: relative;
	width: 16px;
	height: 16px;
	cursor: pointer;
	border: 1px solid #dcdfe6;
	border-radius: 2px;
	transition: all 0.2s;
}

.select-cell input[type='checkbox']:checked {
	background-color: #409eff;
	border-color: #409eff;
}

.select-cell input[type='checkbox']:indeterminate {
	background-color: #409eff;
	border-color: #409eff;
}

.select-cell input[type='checkbox']:indeterminate::after {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 8px;
	height: 2px;
	background-color: #fff;
	transform: translate(-50%, -50%);
}

.select-cell input[type='checkbox']:checked::after {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 4px;
	height: 8px;
	border: solid #fff;
	border-width: 0 2px 2px 0;
	transform: translate(-50%, -50%) rotate(45deg) translate(1px, -1px);
}

/* HTML内容包装器样式 */
.html-content-wrapper {
	display: inline-block;
	width: 100%;
}

.html-content-wrapper.html-ellipsis {
	display: block;
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.html-content-wrapper.html-ellipsis :deep(img) {
	max-width: 100%;
	height: auto;
	vertical-align: middle;
	display: inline-block;
}

/* 确保图片和文本在同一行 */
.html-content-wrapper :deep(*) {
	display: inline-block;
	vertical-align: middle;
}

/* 多级表头样式 */
.table-header th {
	position: relative;
	text-align: center;
	vertical-align: middle;
	box-sizing: border-box;
	background-color: #f9fafb;
}

/* 拖拽指示线 */
.resize-indicator {
	position: absolute;
	top: 0;
	width: 2px;
	height: 100%;
	background-color: #409eff;
	z-index: 10;
	box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
	pointer-events: none; /* 确保鼠标事件可以穿透指示线 */
}
</style>
