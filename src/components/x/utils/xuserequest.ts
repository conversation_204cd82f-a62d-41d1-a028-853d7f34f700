import { UnwrapRef } from 'vue'

type IUseRequestOptions<T> = {
	/** 是否立即执行 */
	immediate?: boolean
	/** 初始化数据 */
	initialData?: T
	autoPageNo?: boolean
}

/**
 * useRequest是一个定制化的请求钩子，用于处理异步请求和响应。
 * @param func 一个执行异步请求的函数，返回一个包含响应数据的Promise。
 * @param options 包含请求选项的对象 {immediate, initialData}。
 * @param options.immediate 是否立即执行请求，默认为false。
 * @param options.initialData 初始化数据，默认为undefined。
 * @returns 返回一个对象{loading, error, data, run}，包含请求的加载状态、错误信息、响应数据和手动触发请求的函数。
 */
export function xUseRequest<T>(
	func: () => Promise<any>,
	_params: any = {},
	successFunc?: () => void,
	options: IUseRequestOptions<T> = { immediate: false },
) {
	const initParams = JSON.parse(JSON.stringify(unref(_params)))

	const loading = ref(false)
	const error = ref(false)
	const data = ref(options.initialData) as T

	// 传过来的是对象，会保持响应式的，只要原对象通过Object.assign或者.属性修改，那么这里也会响应式变化
	const params = unref(_params)
	let params1 = {}

	function _run(isReset = false) {
		if (loading.value === true) {
			throw new Error('请求正在执行中')
		}
		if (isReset) {
			data.value = options.initialData
		}
		loading.value = true
		return func(X_COMMON_UTILS.isObject(params) ? { ...params, ...params1 } : params)
			.then(async (res) => {
				data.value = res as UnwrapRef<T>
				error.value = false
				// 判断func的名字是否包含create,update,delete
				if (func.name.includes('create') || func.name.includes('update') || func.name.includes('delete')) {
					toast.success('操作成功')
				}
				successFunc && successFunc()
				return data.value
			})
			.catch((err) => {
				error.value = err
				throw err
			})
			.finally(() => {
				loading.value = false
			})
	}
	function run(_params?: any) {
		if (_params) {
			params1 = _params
		}
		return _run()
	}

	const reset = (_params = {}) => {
		// 判断params是否为event
		const form = _params?.currentTarget?.closest('form')
		if (form) {
			if (form) form.dispatchEvent(new Event('reset', { cancelable: true }))
		} else {
			Object.assign(params, initParams, _params)
		}
		run(true)
	}

	options.immediate && run()
	return { loading, error, data, run, params, reset }
}
export interface IApiPage<T> {
	total: number
	list: T[]
}

/**
 * useRequest是一个定制化的请求钩子，用于处理异步请求和响应。
 * @param func 一个执行异步请求的函数，返回一个包含响应数据的Promise。
 * @param options 包含请求选项的对象 {immediate, initialData}。
 * @param options.immediate 是否立即执行请求，默认为false。
 * @param options.initialData 初始化数据，默认为undefined。
 * @returns 返回一个对象{loading, error, data, run}，包含请求的加载状态、错误信息、响应数据和手动触发请求的函数。
 */
export function xUsePageRequest<T>(
	// eslint-disable-next-line no-unused-vars
	func: (params: any) => Promise<IApiPage<T>>,
	params: any = {},
	options: IUseRequestOptions<T[]> = { immediate: false, autoPageNo: false },
) {
	const isLoadAll = ref(false)
	params = unref(params)
	if (!params?.pageNo) {
		params.pageNo = 1
	}
	if (!params?.pageSize) {
		params.pageSize = 10
	}
	const initParams = JSON.parse(JSON.stringify(params))
	const queryParams = params
	let params1 = {}
	const loading = ref(false)
	const error = ref(false)
	const list = ref([]) as T[]
	const total = ref(0)
	const _run = async (isReset = false) => {
		if (loading.value === true) return
		if (isReset || !options.autoPageNo) {
			isLoadAll.value = false
			list.value.length = 0
		} else {
			if (options.autoPageNo && (isLoadAll.value || loading.value)) {
				return list.value
			}
		}
		loading.value = true
		console.log('queryParams---', queryParams, params1)
		return func(X_COMMON_UTILS.isObject(queryParams) ? { ...queryParams, ...params1 } : queryParams)
			.then((res) => {
				error.value = false
				total.value = res?.total || 0
				if (options?.autoPageNo) {
					list.value.push(...(res?.list || []))
					if (list.value?.length >= res?.total) {
						isLoadAll.value = true
					}
					queryParams.pageNo++
				} else {
					list.value = res?.list || (Array.isArray(res) ? res : [])
				}
				return list.value
			})
			.catch((err) => {
				error.value = err
				throw err
			})
			.finally(() => {
				loading.value = false
			})
	}

	const run = (_params) => {
		console.log('run', _params)
		if (_params) {
			params1 = _params
		}
		return _run()
	}

	const reset = (_params = {}) => {
		// 先找到最近的 BizCardsQuery 组件容器
		const bizCardsContainer = _params?.currentTarget?.closest('[data-component="biz-cards-query"]')

		// 然后在该容器内查找 form 元素
		const form = _params?.currentTarget?.closest('form') || bizCardsContainer?.querySelector('form')
		if (form) {
			if (form) form.dispatchEvent(new Event('reset', { cancelable: true }))
		} else {
			Object.assign(queryParams, initParams, _params)
		}
		run()
	}

	options.immediate && run()
	return { loading, error, list, total, queryParams, run, reset }
}
