export function xThemeSetCssVar(key: string, value: string) {
	document.documentElement.style.setProperty(`${key}`, value)
}
export const themeColors = {
	// 科技感（深色 + 霓虹蓝青）
	primary: '#60A5FA',
	primaryBg: '#0B1220',
	primaryLight: '#3CCAFF',
	success: '#34d19d',
	warning: '#b88302',
	danger: '#E24345',
	error: '#c10e0e',
	info: '#b6b6b6',
	hover: '#152238',
	page: '#0A0F1C',
	title: '#E5E7EB',
	content: '#B8C4D9',
	secondary: '#F4F4F5',
	aid: '#9AA4B2',
	tip: '#0F172A',
	border: '#1E2A3F',
	inputBorder: '#60A5FA',
	inputBg: '#0E1B33',
	inputText: '#E6F0FF',
	inputPlaceholder: '#9FB3C8',
	borderLight: '#2A3B55',
	bg: '#0A1120',
	disabled: '#5C6A7D',
	loading: '#7B8A9A',
	link: '#38BDF8',
	test: 'red',

	// ============ Table Theme ============
	// 深色科技风配色
	tableBg: '#0B1220',
	tableHeaderBg: '#0E1627',
	tableHeaderText: '#CFE9FF',
	tableText: '#D6E3F8',
	tableBorder: '#1E2A3F',
	tableRowHoverBg: '#0F1C31',
	tableFixedColBg: '#0B1220',
}
export const fontSizes = {
	xxs: '0.65rem',
	xs: '0.75rem',
	sm: '0.90rem',
	base: '1rem',
	lg: '1.2rem',
	xl: '1.5rem',
	'2xl': '2rem',
	'3xl': '3rem',
}
export const gapSizes = {
	xxs: '0.2rem',
	xs: '0.5rem',
	sm: '0.8rem',
	base: '1rem',
	lg: '1.2rem',
	xl: '1.5rem',
	'2xl': '2rem',
	'3xl': '3rem',
}

export const directions = ['bottom', 'top', 'left', 'right']

// 色阶值（支持1-9和100-900）
export const colorScales = [50, 100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700, 750, 800, 850, 900, 950]

// 透明度使用 _
export const colorOpacity = [1, 3, 5, 7, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100]

export const allMenuIcons = [
	'i-solar:camera-bold',
	'i-carbon:folder',
	'i-carbon:document',
	'i-material-symbols:file-copy-outline',
	'i-carbon:action-usage',
	'i-carbon:document-multiple-02',
	'i-carbon:airport-location',
	'i-carbon:settings-services',
]
