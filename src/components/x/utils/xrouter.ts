/*
 * 守卫
 * */
import router from '~/plugins/router'
const whitePages = ['login', 'userLogin', 'webHome', 'zsjyQuestionBank', 'zsjyPaperList', '/data2word']
router.beforeEach((to, from, next) => {
	const userStore = useUserStore()
	console.log('to--', to.name)
	if (userStore.token) {
		if (to.name.includes('Login')) {
			goHome()
		}
	} else {
		if (!whitePages.includes(to.name) && !to.name.includes('Login')) {
			if (to.name.includes('web')) {
				userStore.platform = 'web'
			} else {
				userStore.platform = 'admin'
			}
			goLogin()
		}
	}
	next()
})

export function goLogin() {
	const userStore = useUserStore()
	if (userStore.platform === 'admin') {
		router.push({
			name: 'adminLogin',
		})
	}
	if (userStore.platform === 'web') {
		router.replace({
			name: 'webLogin',
		})
	}
}

export function goBack() {
	router.go(-1)
}

export function goHome() {
	const userStore = useUserStore()
	if (userStore.platform === 'admin') {
		router.push({
			name: 'adminHome',
		})
	}
	if (userStore.platform === 'web') {
		router.push({
			name: 'webHome',
		})
	}
}

export function goMy() {
	const userStore = useUserStore()
	if (userStore.platform === 'admin') {
		router.push({
			name: 'adminMy',
		})
	}
	if (userStore.platform === 'web') {
		router.push({
			name: 'webMy',
		})
	}
}

/*
 * biz
 * */
export function goZsjyPaperList(query = {}) {
	router.push({
		name: 'zsjyPaperList',
		query,
	})
}
export function goZsjyExam(query) {
	router.push({
		name: 'examHome',
		query,
	})
}
export function replaceZsjyExam(query) {
	router.push({
		name: 'examHome',
		query,
	})
}
export function goZsjyQuestionBank(query) {
	router.push({
		name: 'zsjyQuestionBank',
		query,
	})
}
export function replaceZsjyExamRules(query) {
	router.push({
		name: 'examRules',
		query,
	})
}
export function goZsjyPaperResult(recordId, userId) {
	router.push({
		name: 'paperResult',
		query: userId > 0 ? { recordId, userId } : { recordId },
	})
}

export function replaceZsjyPaperResult(recordId) {
	router.replace({
		name: 'paperResult',
		query: { recordId, fp: 'paper' },
	})
}
