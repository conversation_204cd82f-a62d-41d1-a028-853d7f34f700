import { fontSizes, themeColors, colorScales } from './themes'
import chroma from 'chroma-js'

// @ts-ignore: 解决 themeColors 索引问题
Object.keys(themeColors).forEach((key) => {
	// 设置基础颜色
	document.documentElement.style.setProperty(`--color-${key}`, themeColors[key as keyof typeof themeColors])

	// 设置色阶变体（支持1-9和100-900）
	colorScales.forEach((scale) => {
		const color = adjustColorScale(themeColors[key], scale)
		themeColors[`${key}-${scale}`] = color
		document.documentElement.style.setProperty(`--color-${key}-${scale}`, color)

		// 提取RGB值并设置为变量
		try {
			const rgb = chroma(color).rgb()
			document.documentElement.style.setProperty(`--color-${key}-${scale}-rgb`, `${rgb[0]}, ${rgb[1]}, ${rgb[2]}`)
		} catch (e) {
			console.error(`颜色格式错误: ${color}`)
		}
	})

	// 为基础颜色也添加RGB变量
	// @ts-ignore: 解决 themeColors 索引问题
	const baseColor = themeColors[key]
	try {
		const rgb = chroma(baseColor).rgb()
		document.documentElement.style.setProperty(`--color-${key}-rgb`, `${rgb[0]}, ${rgb[1]}, ${rgb[2]}`)
	} catch (e) {
		console.error(`基础颜色格式错误: ${baseColor}`)
	}
})

// @ts-ignore: 解决 fontSizes 索引问题
Object.keys(fontSizes).forEach((key) => {
	document.documentElement.style.setProperty(`--font-size-${key}`, fontSizes[key as keyof typeof fontSizes])
})
/**
 * 根据基础十六进制颜色和百分比生成颜色阴影，
 * 类似于 Tailwind CSS / UnoCSS 的颜色阶梯 (例如, 50, 100, ..., 900, 950)。
 * 使用 chroma.scale() 实现更平滑、更健壮的插值。
 *
 * @param hex 基础颜色的十六进制格式 (例如, "#3b82f6", "#ff0000")。
 * @param percentage 一个介于 0 和 100 之间的数字，代表所需的阴影级别。
 *                   较低的值表示更浅的颜色，50 接近基础色，
 *                   较高的值表示更深的颜色。
 * @returns 计算出的颜色阴影的十六进制格式。如果出错则返回原始 hex。
 */
function adjustColorScale(baseHexColor, shadeLevel) {
	const allowedShades = [50, 100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700, 750, 800, 850, 900, 950]
	if (!allowedShades.includes(shadeLevel)) {
		console.error('Invalid shade level.')
		return null
	}

	try {
		const baseColor = chroma(baseHexColor)
		const baseHsl = baseColor.hsl()
		const baseH = isNaN(baseHsl[0]) ? 0 : baseHsl[0]
		const baseS = baseHsl[1]
		const baseL = baseHsl[2]

		// 基于Tailwind蓝色扩展的Lightness目标值（0-1）
		const lightnessTargets = {
			50: 0.969, // #eff6ff
			100: 0.918, // #dbeafe
			150: 0.89, // 插值
			200: 0.863, // #bfdbfe
			250: 0.823, // 插值
			300: 0.784, // #93c5fd
			350: 0.729, // 插值
			400: 0.675, // #60a5fa
			450: 0.636, // 插值
			500: baseL, // 原色
			550: 0.564, // 插值
			600: 0.531, // #2563eb
			650: 0.504, // 插值
			700: 0.476, // #1d4ed8
			750: 0.441, // 插值
			800: 0.406, // #1e40af
			850: 0.369, // 插值
			900: 0.333, // #1e3a8a
			950: 0.208, // #172554
		}

		// 基于Tailwind蓝色扩展的Saturation目标值（0-1）
		const saturationTargets = {
			50: 1.0, // #eff6ff
			100: 0.93, // #dbeafe
			150: 0.945, // 插值
			200: 0.96, // #bfdbfe
			250: 0.96, // 延续高饱和
			300: 0.96, // #93c5fd
			350: 0.95, // 插值
			400: 0.94, // #60a5fa
			450: 0.92, // 插值
			500: baseS, // 原色
			550: 0.865, // 插值
			600: 0.83, // #2563eb
			650: 0.795, // 插值
			700: 0.76, // #1d4ed8
			750: 0.725, // 插值
			800: 0.69, // #1e40af
			850: 0.66, // 插值
			900: 0.63, // #1e3a8a
			950: 0.58, // #172554
		}

		// 灰度处理逻辑保持不变
		let targetL = lightnessTargets[shadeLevel]
		let targetS = saturationTargets[shadeLevel]
		if (baseS < 0.1) {
			targetS = baseS * (targetL > 0.5 ? 1 : 0.7)
		}

		// 生成新颜色
		const newColor = chroma.hsl(baseH, Math.max(0, Math.min(1, targetS)), Math.max(0, Math.min(1, targetL)))

		return newColor.hex()
	} catch (error) {
		console.error(`Error processing color ${baseHexColor} for shade ${shadeLevel}:`, error)
		return null
	}
}
