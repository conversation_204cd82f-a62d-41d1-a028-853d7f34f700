<script setup lang="ts">
/*const model1 = defineModel('count1', {
	type: String,
	default: 'aaa',
})*/
defineProps({
	zIndex: {
		type: Number,
		default: 5,
	},
})
</script>

<template>
	<teleport to="body">
		<div class="fixed left-0 top-0 hfull wfull flex items-center justify-center bg-[rgba(0,0,0,0.9)]" :style="{ zIndex }">
			<slot></slot>
		</div>
	</teleport>
</template>

<style scoped></style>
