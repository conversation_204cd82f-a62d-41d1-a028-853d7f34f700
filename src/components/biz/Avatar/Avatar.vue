<script setup lang="ts">
import { ref } from 'vue'
import { X_FILE_UTILS } from '@/components/x/utils/xutils'

const props = defineProps({
	size: {
		type: String,
		default: '2rem',
	},
	enableModify: {
		type: Boolean,
		default: false,
	},
})

const userStore = useUserStore()
const uploadStatus = ref('')

const handleAvatarUpload = async (event: Event) => {
	const input = event.target as HTMLInputElement
	if (!input.files?.length) return

	const file = input.files[0]
	uploadStatus.value = '上传中'

	try {
		const fileObj = {
			raw: file,
			name: file.name,
			progress: 0,
		}
		if (props.platform === 'admin') {
			await X_FILE_UTILS.upload(fileObj)
		} else {
			await X_FILE_UTILS.uploadUser(fileObj)
		}
		toast.success('头像上传成功')
		uploadStatus.value = '修改中'
		// Call update avatar API
		await BIZ_USER_APIS.updateUserAvatar({ avatar: fileObj.remoteUrl })
		toast.success('头像修改成功')
		userStore.userinfo.avatar = fileObj.remoteUrl
		uploadStatus.value = ''
	} catch (error) {
		uploadStatus.value = ''
		toast.error(error)
		console.error('Avatar upload failed:', error)
	}
}
</script>

<template>
	<!--self-stretch-->
	<x-popup trigger="hover" placement="bottom-start">
		<div class="relative">
			<XImage v-bind="$attrs" class="rounded-50%" :style="{ width: size, height: size }" :src="userStore.getUserinfo?.avatar" mode="aspectFill" />
			<div v-if="uploadStatus" class="absolute inset-0 flex items-center justify-center rounded-50% bg-black/50 text-white text-xxs">
				{{ uploadStatus }}
			</div>
		</div>
		<template #content>
			<x-card class="rounded bg-white p-xs">
				<div v-if="enableModify" class="cursor-pointer p-xxs-xs hover:bg-page">
					<label class="w-full cursor-pointer">
						修改头像
						<input type="file" class="hidden" accept="image/*" @change="handleAvatarUpload" />
					</label>
				</div>
				<template v-else>
					<div class="cursor-pointer p-xxs-xs hover:bg-page" @click="userStore.goMy()">个人中心</div>

					<div class="cursor-pointer p-xxs-xs hover:bg-page" @click="userStore.logout(userStore.platform === 'admin')">退出登录</div>
				</template>
			</x-card>
		</template>
	</x-popup>
</template>

<style scoped></style>
