<script setup lang="ts"></script>

<template>
	<div class="relative flex justify-between rounded p-sm border-border" data-component="biz-cards-query">
		<XCollapse min-height="2.5rem">
			<template #trigger="{ isOpen }">
				<div class="absolute right-0.1rem top-0.1rem z-2 flex items-center justify-end text-sm gap-xxs">
					<span>{{ isOpen ? '收起' : '展开' }}</span>
					<div class="i-bi:chevron-down transition-transform duration-300" :class="{ 'rotate-180': isOpen }" />
				</div>
			</template>
			<slot></slot>
		</XCollapse>
		<!--		<slot></slot>-->
	</div>
</template>

<style scoped></style>
