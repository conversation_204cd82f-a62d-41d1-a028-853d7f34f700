<template>
	<footer class="glass mt-2 flex flex-shrink-0 items-center justify-between rounded-lg p-2 text-sm">
		<div class="flex items-center space-x-8">
			<!-- 系统状态 -->
			<div class="flex items-center">
				<svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-5 w-5" :class="systemStatusIcon.class" viewBox="0 0 20 20" fill="currentColor">
					<path :fill-rule="systemStatusIcon.fillRule" :d="systemStatusIcon.path" :clip-rule="systemStatusIcon.clipRule" />
				</svg>
				<span>{{ systemStatus.text }}</span>
			</div>

			<!-- 运输量统计 -->
			<div v-if="false" class="flex items-center">
				<svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
					<path
						d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"
					/>
				</svg>
				<span>{{ transportStats.text }}</span>
			</div>
		</div>

		<!-- 版权信息 -->
		<div>
			<span class="opacity-70 text-sm">{{ copyright }}</span>
		</div>
	</footer>
</template>

<script setup lang="ts">
// Props定义
interface Props {
	systemStatus?: {
		status: 'normal' | 'warning' | 'error'
		text: string
	}
	transportStats?: {
		amount: number
		unit: string
		period: string
	}
	copyright?: string
}

const props = withDefaults(defineProps<Props>(), {
	systemStatus: () => ({
		status: 'normal',
		text: '系统运行正常',
	}),
	transportStats: () => ({
		amount: 8526,
		unit: '吨',
		period: '今日',
	}),
	copyright: '© 2025 晋能电力',
})

// 系统状态图标配置
const systemStatusIcon = computed(() => {
	switch (props.systemStatus.status) {
		case 'normal':
			return {
				class: 'text-success',
				fillRule: 'evenodd',
				clipRule: 'evenodd',
				path: 'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z',
			}
		case 'warning':
			return {
				class: 'text-warning',
				fillRule: 'evenodd',
				clipRule: 'evenodd',
				path: 'M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z',
			}
		case 'error':
			return {
				class: 'text-danger',
				fillRule: 'evenodd',
				clipRule: 'evenodd',
				path: 'M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z',
			}
		default:
			return {
				class: 'text-gray-400',
				fillRule: 'evenodd',
				clipRule: 'evenodd',
				path: 'M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z',
			}
	}
})

// 运输统计文本
const transportStats = computed(() => {
	const { amount, unit, period } = props.transportStats
	return {
		text: `${period}运输量: ${amount.toLocaleString()} ${unit}`,
	}
})
</script>

<style scoped>
/* 组件特定样式可以在这里添加 */
</style>
