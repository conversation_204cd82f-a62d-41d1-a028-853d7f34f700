<template>
	<CardContainer title="运单管理" icon="i-gis:route" class="glass h-full w-full flex flex-col overflow-hidden rounded-lg p-3">
		<div class="hfull flex flex-col overflow-hidden">
			<!-- 搜索框 -->
			<div class="flex-shrink-0 border-white/10 p-3 border-b">
				<div class="relative">
					<input
						v-model="searchKeyword"
						type="text"
						placeholder="请输入车牌号搜索"
						class="w-full border border-white/20 rounded-lg bg-white/10 px-4 py-1 pl-10 text-white transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-white/50"
						@input="handleSearch"
					/>
					<!-- 搜索图标 -->
					<div class="absolute left-3 top-1/2 transform text-white/50 -translate-y-1/2">
						<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</div>
					<!-- 清除按钮 -->
					<button
						v-if="searchKeyword"
						class="absolute right-3 top-1/2 transform text-white/50 transition-colors -translate-y-1/2 hover:text-white"
						@click="clearSearch"
					>
						<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
						</svg>
					</button>
				</div>
			</div>
			<!-- 运单列表容器 -->
			<div class="flex-1 overflow-hidden">
				<!-- 空状态 -->
				<!--			<div v-if="filteredOrderList && filteredOrderList.length === 0" class="h-full flex flex-col items-center justify-center text-white/60">
				<div class="mb-4 text-4xl">📋</div>
				<div class="text-lg">{{ searchKeyword ? '未找到匹配的运单' : '暂无运单数据' }}</div>
				<div v-if="searchKeyword" class="mt-2 text-sm">尝试搜索其他车牌号</div>
			</div>-->
				<x-empty v-if="filteredOrderList && filteredOrderList.length === 0" />
				<!-- 运单列表 -->
				<div v-else class="h-full overflow-y-auto p-2 space-y-2">
					<div
						v-for="order in filteredOrderList"
						:key="order.id"
						:class="[
							'rounded-lg p-3 cursor-pointer transition-colors flex-shrink-0',
							transportOrderStore.selectedOrder?.id === order.id ? 'bg-blue-500/20 border border-blue-400' : 'hover:bg-white/10',
						]"
						@click="handleOrderClick(order)"
					>
						<!-- 第一行：运单号、车牌、状态 -->
						<div class="mb-2 flex items-center justify-between">
							<div class="flex items-center text-white/80 text-xs space-x-3">
								<span class="font-medium">{{ order.orderNo }}</span>
								<span>{{ order.driverName }}</span>
								<span v-if="order.vehicle?.licensePlate">{{ order.vehicle.licensePlate }}</span>
							</div>
							<div class="rounded-full px-2 py-1 text-white text-xs" :style="{ backgroundColor: transportOrderStore.getOrderStatusColor(order) }">
								{{ transportOrderStore.formatOrderStatus(order.status) }}
							</div>
						</div>
						<!-- 第二行：出发地 - 目的地 -->
						<div class="mb-2">
							<div class="mb-1 flex items-center text-white text-sm">
								<div class="mr-2 h-2 w-2 flex-shrink-0 rounded-full bg-green-500"></div>
								<span class="flex-1 truncate">{{ order.departure && order.departure.name }}</span>
								<span class="mx-2 text-white/60">→</span>
								<div class="mr-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
								<span class="flex-1 truncate">{{ order.destination && order.destination.name }}</span>
							</div>
							<!-- 时间信息行 -->
							<div class="flex items-center text-white/70 text-xs">
								<div class="mr-2 h-2 w-2 flex-shrink-0 opacity-0"></div>
								<span class="flex-1 truncate">{{ transportOrderStore.formatEstimateArriveTime(order.deliveryTime) }}</span>
								<span class="mx-2 opacity-0">→</span>
								<div class="mr-2 h-2 w-2 flex-shrink-0 opacity-0"></div>
								<span class="flex-1 truncate">{{ transportOrderStore.formatEstimateArriveTime(order.estimatedArrivalTime) }}</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</CardContainer>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed } from 'vue'
import CardContainer from '../common/CardContainer.vue'
import { useTransportOrderStore, type TransportOrder } from '@/stores/useTransportOrderStore'

/**
 * 运单中心组件
 * 显示运单列表，包含运单ID、出发地、目的地、状态等信息
 * 每分钟轮询一次最新的运单数据
 * 支持根据车牌号搜索运单
 */
// 使用运单store
// 使用运单数据store
const transportOrderStore = useTransportOrderStore()
// 全局电厂选择
const plantStore = usePlantStore()
const { selectedDeptId } = storeToRefs(plantStore)

// 搜索关键词
const searchKeyword = ref('')

/**
 * 过滤后的运单列表
 * 根据搜索关键词过滤车牌号
 */
const filteredOrderList = computed(() => {
	const baseList = transportOrderStore.allUncancelledOrders // 只显示未取消的运单
	const plantFiltered = selectedDeptId.value ? baseList.filter((o) => !o.deptId || o.deptId === selectedDeptId.value) : baseList
	if (!searchKeyword.value.trim()) {
		return plantFiltered
	}
	return plantFiltered.filter((o) => o.vehicle?.licensePlate?.includes(searchKeyword.value.trim()))
})

/**
 * 处理搜索输入
 */
const handleSearch = () => {
	// 搜索逻辑已通过 computed 属性自动处理
	console.log('搜索车牌号:', searchKeyword.value)
}

/**
 * 清除搜索
 */
const clearSearch = () => {
	searchKeyword.value = ''
}

// 轮询定时器
let pollTimer: ReturnType<typeof setInterval> | null = null

/**
 * 处理运单点击事件
 * @param order 被点击的运单
 */
const handleOrderClick = (order: TransportOrder) => {
	// 选中运单
	transportOrderStore.selectOrder(order.id)
	console.log('选中运单:', order)
}

/**
 * 启动轮询
 * 立即执行一次，然后每分钟执行一次
 */
const startPolling = () => {
	// 立即执行一次
	transportOrderStore.pollTransportOrder()
	// 设置定时器，每分钟执行一次
	pollTimer = setInterval(() => {
		transportOrderStore.pollTransportOrder()
	}, 60000) // 60秒 = 1分钟
}

/**
 * 停止轮询
 */
const stopPolling = () => {
	if (pollTimer) {
		clearInterval(pollTimer)
		pollTimer = null
	}
}

// 组件挂载时启动轮询
onMounted(() => {
	startPolling()
})

// 组件卸载时停止轮询
onUnmounted(() => {
	stopPolling()
})
</script>

<style scoped>
.glass {
	background: rgba(255, 255, 255, 0.05);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
	box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}
</style>
