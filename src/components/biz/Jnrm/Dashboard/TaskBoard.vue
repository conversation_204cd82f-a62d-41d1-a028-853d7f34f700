<template>
	<CardContainer title="任务看板" icon="i-hugeicons:task-daily-01" class="h-full">
		<div class="h-full flex flex-col">
			<!-- 任务完成率环形图 -->
			<div class="mb-5 flex-shrink-0">
				<CircularProgress :percentage="transportOrderStore.taskCompletionRate" color="#0a81ab" name="完成率" />
			</div>

			<!-- 任务数据统计 -->
			<div class="grid grid-cols-3 mb-4 gap-2 flex-shrink-0">
				<DataCard name="总任务" :value="transportOrderStore.totalCount" />
				<DataCard name="完成" :value="transportOrderStore.deliveredCount" color="success" />
				<DataCard name="异常" :value="transportOrderStore.exceptionCount" color="danger" />
			</div>

			<!-- 车辆状态分布 -->
			<div class="flex-1">
				<h4 class="mb-2 font-semibold text-sm">运单状态分布</h4>
				<div class="space-y-2">
					<ProgressBar
						:name="`在途 (${transportOrderStore.inTransitCount}单)`"
						color="#10b981"
						:percentage="transportOrderStore.vehicleStatusDistribution.在途"
					/>
					<ProgressBar
						:name="`待发车 (${transportOrderStore.pendingCount}单)`"
						color="#f59e0b"
						:percentage="transportOrderStore.vehicleStatusDistribution.待发车"
					/>
					<ProgressBar
						:name="`已送达 (${transportOrderStore.deliveredCount}单)`"
						color="#6b7280"
						:percentage="transportOrderStore.vehicleStatusDistribution.已送达"
					/>
					<ProgressBar
						:name="`异常 (${transportOrderStore.exceptionCount}单)`"
						color="#dc2626"
						:percentage="transportOrderStore.vehicleStatusDistribution.异常"
					/>
				</div>
			</div>
		</div>
	</CardContainer>
</template>

<script setup lang="ts">
import CardContainer from '../common/CardContainer.vue'
import CircularProgress from '../common/CircularProgress.vue'
import DataCard from '../common/DataCard.vue'
import ProgressBar from '../common/ProgressBar.vue'
import { useTransportOrderStore } from '@/stores/useTransportOrderStore'

/**
 * 任务看板组件
 * 显示基于运单数据的任务统计信息：
 * - 任务完成率（已送达运单比例）
 * - 总任务数、完成数、异常数
 * - 运单状态分布（在途、待发车、已送达、异常）
 */

// 使用运单数据store
const transportOrderStore = useTransportOrderStore()
</script>
