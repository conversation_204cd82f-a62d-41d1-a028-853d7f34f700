<template>
	<CardContainer title="供需监控" :icon-component="SupplyDemandIcon" class="mt-2 flex-1">
		<!-- Tab切换按钮 -->
		<TabSwitcher v-model="activeTab" :tabs="tabs" :default-tab="'power-plant'" @tab-change="handleTabChange" />

		<!-- 电厂列表 -->
		<div v-show="activeTab === 'power-plant'" class="space-y-2">
			<div class="mb-1 flex justify-between px-1 text-gray-400 text-xs">
				<span>电厂名称</span>
				<div class="flex space-x-4">
					<span>接收量</span>
					<span>需求量</span>
				</div>
			</div>

			<SupplyDemandItem
				v-for="plant in powerPlants"
				:key="plant.id"
				:name="plant.name"
				:current-value="plant.currentValue"
				:target-value="plant.targetValue"
				:percentage="plant.percentage"
				:status="plant.status"
			/>
		</div>

		<!-- 煤场列表 -->
		<div v-show="activeTab === 'coal-mine'" class="space-y-2">
			<div class="mb-1 flex justify-between px-1 text-gray-400 text-xs">
				<span>煤场名称</span>
				<div class="flex space-x-4">
					<span>已供量</span>
					<span>供应量</span>
				</div>
			</div>

			<SupplyDemandItem
				v-for="mine in coalMines"
				:key="mine.id"
				:name="mine.name"
				:current-value="mine.currentValue"
				:target-value="mine.targetValue"
				:percentage="mine.percentage"
				:status="mine.status"
			/>
		</div>
	</CardContainer>
</template>

<script setup lang="ts">
import CardContainer from '../common/CardContainer.vue'
import TabSwitcher from '../common/TabSwitcher.vue'
import SupplyDemandItem from '../common/SupplyDemandItem.vue'

// 供需监控图标组件
const SupplyDemandIcon = defineComponent({
	render() {
		return h(
			'svg',
			{
				xmlns: 'http://www.w3.org/2000/svg',
				viewBox: '0 0 20 20',
				fill: 'currentColor',
			},
			[
				h('path', {
					'fill-rule': 'evenodd',
					d: 'M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z',
					'clip-rule': 'evenodd',
				}),
			],
		)
	},
})

// Tab配置
const tabs = [
	{ key: 'power-plant', label: '电厂' },
	{ key: 'coal-mine', label: '煤场' },
]

// 当前活跃的Tab
const activeTab = ref('power-plant')

// 电厂数据
const powerPlants = [
	{
		id: 1,
		name: '朔州发电厂',
		currentValue: '8,500吨',
		targetValue: '12,000吨',
		percentage: 70,
		status: 'success' as const,
	},
	{
		id: 2,
		name: '山阴电厂',
		currentValue: '4,200吨',
		targetValue: '8,000吨',
		percentage: 52,
		status: 'warning' as const,
	},
	{
		id: 3,
		name: '右玉电厂',
		currentValue: '2,800吨',
		targetValue: '7,500吨',
		percentage: 37,
		status: 'danger' as const,
	},
]

// 煤场数据
const coalMines = [
	{
		id: 1,
		name: '同煤集团露天矿',
		currentValue: '10,200吨',
		targetValue: '15,000吨',
		percentage: 68,
		status: 'primary' as const,
	},
	{
		id: 2,
		name: '平朔安太堡露天矿',
		currentValue: '8,600吨',
		targetValue: '12,000吨',
		percentage: 72,
		status: 'info' as const,
	},
	{
		id: 3,
		name: '大同煤矿集团',
		currentValue: '5,800吨',
		targetValue: '8,000吨',
		percentage: 73,
		status: 'success' as const,
	},
]

// Tab切换处理
const handleTabChange = (tabKey: string) => {
	activeTab.value = tabKey
}
</script>

<style scoped>
/* 组件样式已通过Tailwind CSS类处理 */
</style>
