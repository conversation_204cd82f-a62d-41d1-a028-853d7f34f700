<template>
	<CardContainer title="关键指标" icon="i-material-symbols:action-key-outline" class="h-full">
		<div class="key-metrics-content h-full flex flex-col">
			<!-- 大型数字指标 -->
			<div class="mb-6 flex-shrink-0">
				<div class="mb-3 text-center">
					<p class="text-gray-400 text-sm">在途车辆总数</p>
					<p class="number-bounce text-4xl font-bold">{{ inTransitCount }}</p>
				</div>
				<div class="grid grid-cols-3 gap-2">
					<DataCard name="正常车辆" :value="normalRunningCount" color="success" />
					<DataCard name="异常停留" :value="abnormalStoppedCount" color="warning" />
					<DataCard name="铅封异常" :value="sealAbnormalCount" color="danger" />
				</div>
			</div>

			<!-- 关键指标列表 -->
			<div class="flex-1 space-y-4">
				<MetricProgressBar
					name="在线运单"
					:value="`${onlineCount}`"
					:percentage="totalCount > 0 ? (onlineCount / totalCount) * 100 : 0"
					comparison="实时数据"
					trend="up"
					color="success"
				/>

				<MetricProgressBar
					name="异常事件数量"
					:value="`${alertExceptionCount}起`"
					:percentage="totalCount > 0 ? (alertExceptionCount / totalCount) * 100 : 0"
					comparison="需要处理"
					trend="down"
					color="danger"
				/>

				<MetricProgressBar
					name="任务完成率"
					:value="`${taskCompletionRate}%`"
					:percentage="taskCompletionRate"
					comparison="今日统计"
					trend="up"
					color="success"
				/>
			</div>
		</div>
	</CardContainer>
</template>

<script setup lang="ts">
import CardContainer from '../common/CardContainer.vue'
import DataCard from '../common/DataCard.vue'
import MetricProgressBar from '../common/MetricProgressBar.vue'
import { useTransportOrderStore } from '@/stores/useTransportOrderStore'
import useAlertStore from '@/stores/useAlertStore'

/**
 * 关键指标组件
 * 显示基于运单数据的关键指标：
 * - 在途车辆总数
 * - 正常车辆、异常停留、铅封异常数量
 * - 在线运单、异常事件、任务完成率
 * - WebSocket连接状态
 */

// 使用运单数据store
const transportOrderStore = useTransportOrderStore()
// 使用告警数据store
const alertStore = useAlertStore()
// 全局电厂选择
const plantStore = usePlantStore()
const { selectedDeptId } = storeToRefs(plantStore)

// 基于电厂过滤后的统计口径
const inTransitCount = computed(() => {
	const base = selectedDeptId.value ? transportOrderStore.orderList.filter((o) => o.deptId === selectedDeptId.value) : transportOrderStore.orderList
	return base.filter((o) => o.status === '在途').length
})
const normalRunningCount = computed(() => {
	const base = selectedDeptId.value ? transportOrderStore.orderList.filter((o) => o.deptId === selectedDeptId.value) : transportOrderStore.orderList
	return base.filter((o) => o.status === '在途' && o.vehicleOnlineStatus === 'online').length
})
const abnormalStoppedCount = computed(() => {
	const base = selectedDeptId.value ? transportOrderStore.orderList.filter((o) => o.deptId === selectedDeptId.value) : transportOrderStore.orderList
	return base.filter((o) => o.status === '异常' || (o.status === '在途' && o.vehicleOnlineStatus === 'offline')).length
})
const onlineCount = computed(() => {
	const base = selectedDeptId.value ? transportOrderStore.orderList.filter((o) => o.deptId === selectedDeptId.value) : transportOrderStore.orderList
	return base.filter((o) => o.vehicleOnlineStatus === 'online').length
})
const totalCount = computed(() => {
	const base = selectedDeptId.value ? transportOrderStore.orderList.filter((o) => o.deptId === selectedDeptId.value) : transportOrderStore.orderList
	return base.length
})
// 从alertStore获取异常事件数量（未处理的告警）
const alertExceptionCount = computed(() => {
	const base = selectedDeptId.value ? alertStore.list.filter((a) => a.deptId === selectedDeptId.value) : alertStore.list
	return base.filter((a) => !a.handleStatus).length
})

// 从alertStore获取铅封异常数量
const sealAbnormalCount = computed(() => {
	const base = selectedDeptId.value ? alertStore.list.filter((a) => a.deptId === selectedDeptId.value) : alertStore.list
	return base.filter((a) => a.alertType === '铅封告警' && !a.handleStatus).length
})

// 任务完成率：沿用 store 的计算规则，但基于过滤后的数量
const taskCompletionRate = computed(() => {
	const delivered = (() => {
		const base = selectedDeptId.value ? transportOrderStore.orderList.filter((o) => o.deptId === selectedDeptId.value) : transportOrderStore.orderList
		return base.filter((o) => o.status === '已送达').length
	})()
	const total = totalCount.value
	if (!total) return 0
	return Math.round((delivered / total) * 100)
})
</script>

<style scoped>
.number-bounce {
	animation: numberBounce 1s ease-out;
}

@keyframes numberBounce {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.1);
	}
	100% {
		transform: scale(1);
	}
}
</style>
