<template>
	<CardContainer icon="i-carbon:warning-multiple" title="异常告警消息" class="h-full flex flex-col overflow-hidden">
		<div class="hfull flex flex-col overflow-hidden">
			<!-- 告警统计 -->
			<AlertStats :stats="computedAlertStats" :selected-stat="statFilter.type" @stat-click="handleStatClick" />

			<!-- 筛选器 -->
			<AlertFilter v-model="queryModel" @change="handleFilterChange" />
			<!-- 告警列表 -->
			<div v-if="filteredAlerts?.length > 0" class="flex-1 overflow-y-auto space-y-2">
				<AlertItem v-for="alert in filteredAlerts" :key="alert.id" :alert="alert" @handle="handleAlert" />
			</div>

			<!-- 空状态 -->
			<x-empty v-else />
		</div>

		<!-- 告警处理弹窗 -->
		<XModal
			v-model="handleModalVisible"
			title="告警处理"
			container-class="!bg-gray-800 !text-white"
			title-class="!text-white !border-primary/40"
			show-submit
			@submit="submitHandle"
		>
			<div class="pb-base space-y-4">
				<!-- 告警信息 -->
				<AlertItem :alert="currentAlert" :show-actions="false" />
				<!-- 处理方式输入框 -->
				<div>
					<label class="mb-2 block text-gray-300 font-medium">处理方式</label>
					<textarea
						v-model="handleRemark"
						placeholder="请输入处理方式..."
						class="h-24 w-full resize-none border border-gray-600 rounded-lg bg-gray-800 px-3 py-2 text-white focus:outline-none focus:border-primary placeholder-gray-400"
					></textarea>
				</div>

				<!-- 快捷处理按钮 -->
				<div>
					<label class="mb-2 block text-gray-300 font-medium">快捷录入</label>
					<div class="grid grid-cols-2 gap-2">
						<button
							v-for="quickAction in quickActions"
							:key="quickAction.value"
							class="flex items-center justify-center border rounded-lg px-3 py-2 transition-all duration-200 hover:scale-102"
							:class="quickAction.class"
							@click="selectQuickAction(quickAction.value)"
						>
							<div :class="quickAction.icon" class="mr-2"></div>
							{{ quickAction.label }}
						</button>
					</div>
				</div>
			</div>
		</XModal>
	</CardContainer>
</template>

<script setup lang="ts">
import CardContainer from '../common/CardContainer.vue'
import AlertStats from '../common/AlertStats.vue'
import AlertFilter from '../common/AlertFilter.vue'
import AlertItem from '../common/AlertItem.vue'

// 当前筛选器（用于下方的筛选器组件）
const queryModel = ref({
	level: null,
})
// 使用全局电厂选择
const plantStore = usePlantStore()
const { selectedDeptId } = storeToRefs(plantStore)
const currentFilter = ref(null)
// 顶部统计卡片的筛选状态（独立于下方筛选器）
const statFilter = ref({
	type: null, // 'total', 'danger', 'unhandled'
	showAll: false, // 是否显示所有告警（包括已处理的）
})
const alertStore = useAlertStore()
// 直接使用store中的数据，确保实时更新
const alertList = computed(() => alertStore.list)

// 告警处理相关状态
const handleModalVisible = ref(false)
const currentAlert = ref(null)
const handleRemark = ref('')

// 快捷处理选项
const quickActions = [
	{
		value: '已处理完成，问题已解决',
		label: '已处理',
		icon: 'i-carbon:checkmark-filled',
		class: 'bg-primary/15 border-primary/40 text-primary hover:bg-primary/25 hover:border-primary/60',
	},
	{
		value: '经核实为误报，无需处理',
		label: '误报',
		icon: 'i-carbon:warning-alt',
		class: 'bg-primary/15 border-primary/40 text-primary hover:bg-primary/25 hover:border-primary/60',
	},
]

onMounted(async () => {
	// 获取告警列表，内部会自动启动15秒轮询
	await alertStore.getList()
})

onUnmounted(() => {
	// 停止轮询机制
	alertStore.stopPolling()
})

// 处理告警
const handleAlert = (alert) => {
	currentAlert.value = alert
	handleRemark.value = ''
	handleModalVisible.value = true
}

// 选择快捷处理方式
const selectQuickAction = (value) => {
	handleRemark.value = value
}

// 处理统计卡片点击
const handleStatClick = (statType) => {
	// 如果点击的是当前已选中的统计卡片，则取消选中
	if (statFilter.value.type === statType) {
		statFilter.value.type = null
		statFilter.value.showAll = false
		return
	}

	// 清除下方筛选器的选择
	queryModel.value.level = null
	// 同时清空全局电厂选择
	usePlantStore().reset()
	currentFilter.value = null

	// 设置新的统计筛选状态
	statFilter.value.type = statType

	if (statType === 'danger') {
		// 点击严重告警，显示所有严重告警（包括已处理的）
		statFilter.value.showAll = true
	} else if (statType === 'unhandled') {
		// 点击未处理，只显示未处理的告警
		statFilter.value.showAll = false
	} else if (statType === 'total') {
		// 点击总告警，显示所有告警
		statFilter.value.showAll = true
	}
}

// 处理下方筛选器变化
const handleFilterChange = () => {
	// 清除顶部统计卡片的选择
	statFilter.value.type = null
	statFilter.value.showAll = false
}

// 提交处理
const submitHandle = async () => {
	if (!handleRemark.value.trim()) {
		toast.warning('请输入处理方式')
		return
	}

	if (!currentAlert.value?.id) {
		toast.error('告警信息异常，请重试')
		return
	}

	try {
		// 调用真实的处理告警API
		await alertStore.handleAlert(currentAlert.value.id, handleRemark.value.trim())

		toast.success('告警处理成功')
		handleModalVisible.value = false

		// 清空处理备注
		handleRemark.value = ''
		currentAlert.value = null

		// 刷新告警列表以确保数据同步
		await alertStore.refreshList()
	} catch (error) {
		console.error('处理告警失败:', error)
		// 根据错误类型提供更具体的错误信息
		const errorMessage = error?.message || error?.data?.msg || '处理告警失败，请重试'
		toast.error(errorMessage)
	}
}

// 计算告警统计（总告警、严重告警、未处理）- 基于 selectedDeptId 统一口径
const computedAlertStats = computed(() => {
	const base = selectedDeptId.value ? alertList.value.filter((a) => a.deptId === selectedDeptId.value) : alertList.value
	const totalCount = base.length
	const dangerCount = base.filter((alert) => alert.level === '严重').length
	const unhandledCount = base.filter((alert) => !alert.handleStatus).length

	return [
		{
			type: 'total',
			label: '总告警',
			count: totalCount,
			level: null,
		},
		{
			type: 'danger',
			label: '严重告警',
			count: dangerCount,
			level: '严重',
		},
		{
			type: 'unhandled',
			label: '未处理',
			count: unhandledCount,
			level: 'unhandled',
		},
	]
})

// 筛选后的告警列表
const filteredAlerts = computed(() => {
	return alertList.value.filter((alert) => {
		// 统计卡片筛选逻辑
		let statFilterMatch = true
		if (statFilter.value.type === 'danger') {
			// 严重告警筛选：显示所有严重告警
			statFilterMatch = alert.level === '严重'
		} else if (statFilter.value.type === 'unhandled') {
			// 未处理筛选：只显示未处理的告警
			statFilterMatch = !alert.handleStatus
		} else if (statFilter.value.type === 'total') {
			// 总告警筛选：显示所有告警
			statFilterMatch = true
		} else {
			// 默认状态：只显示未处理的告警
			statFilterMatch = !alert.handleStatus
		}

		// 处理状态筛选（基于统计卡片的选择）
		const handleStatusFilter = statFilter.value.showAll ? true : !alert.handleStatus

		// 下方筛选器的筛选逻辑（只对当前显示的告警进行筛选）
		const levelFilter = !queryModel.value?.level || alert.level === queryModel.value?.level
		const deptFilter = !selectedDeptId.value || alert.deptId === selectedDeptId.value

		return statFilterMatch && handleStatusFilter && levelFilter && deptFilter
	})
})
</script>
