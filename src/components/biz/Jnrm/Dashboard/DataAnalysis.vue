<template>
	<CardContainer title="数据分析" :icon-component="DataAnalysisIcon" class="h-full flex flex-col">
		<!-- 时间筛选器 -->
		<div class="mb-3 flex items-center justify-end">
			<select v-model="selectedPeriod" class="border border-gray-700 rounded bg-gray-800 px-1 py-0.5 text-xs" @change="handlePeriodChange">
				<option value="today">今日</option>
				<option value="week">本周</option>
				<option value="month">本月</option>
			</select>
		</div>

		<!-- 路线热力图 -->
		<RouteHeatmap :data="routeData" :labels="routeLabels" />

		<!-- 时段统计 -->
		<TimeDistribution :data="timeData" :labels="timeLabels" />

		<!-- 异常事件分布 -->
		<AnomalyDistribution :data="anomalyData" />
	</CardContainer>
</template>

<script setup lang="ts">
import CardContainer from '../common/CardContainer.vue'
import RouteHeatmap from '../common/RouteHeatmap.vue'
import TimeDistribution from '../common/TimeDistribution.vue'
import AnomalyDistribution from '../common/AnomalyDistribution.vue'

// 数据分析图标组件
const DataAnalysisIcon = defineComponent({
	render() {
		return h(
			'svg',
			{
				xmlns: 'http://www.w3.org/2000/svg',
				viewBox: '0 0 20 20',
				fill: 'currentColor',
			},
			[
				h('path', {
					d: 'M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z',
				}),
			],
		)
	},
})

interface RouteData {
	percentage: number
	status: 'normal' | 'warning' | 'danger'
}

interface TimePeriodData {
	percentage: number
	hour: number
}

interface AnomalyData {
	name: string
	percentage: number
	color: 'danger' | 'warning' | 'purple' | 'gray'
}

interface Emits {
	(e: 'period-change', period: string): void
}

const emit = defineEmits<Emits>()

// 当前选择的时间周期
const selectedPeriod = ref('today')

// 模拟路线数据
const routeData = ref<RouteData[]>([
	{ percentage: 60, status: 'normal' },
	{ percentage: 85, status: 'normal' },
	{ percentage: 40, status: 'normal' },
	{ percentage: 95, status: 'danger' },
	{ percentage: 70, status: 'normal' },
	{ percentage: 50, status: 'normal' },
	{ percentage: 30, status: 'normal' },
	{ percentage: 45, status: 'normal' },
	{ percentage: 75, status: 'normal' },
	{ percentage: 90, status: 'warning' },
	{ percentage: 65, status: 'normal' },
	{ percentage: 55, status: 'normal' },
])

const routeLabels = ref(['路线A', '路线F', '路线L'])

// 模拟时段数据
const timeData = ref<TimePeriodData[]>([
	{ percentage: 30, hour: 0 },
	{ percentage: 40, hour: 3 },
	{ percentage: 60, hour: 6 },
	{ percentage: 75, hour: 9 },
	{ percentage: 90, hour: 12 },
	{ percentage: 65, hour: 15 },
	{ percentage: 45, hour: 18 },
	{ percentage: 30, hour: 21 },
])

const timeLabels = ref(['00:00', '06:00', '12:00', '18:00', '24:00'])

// 模拟异常数据
const anomalyData = ref<AnomalyData[]>([
	{
		name: '铅封异常',
		percentage: 28,
		color: 'danger',
	},
	{
		name: '异常停留',
		percentage: 42,
		color: 'warning',
	},
	{
		name: '路线偏离',
		percentage: 15,
		color: 'purple',
	},
	{
		name: '其他异常',
		percentage: 15,
		color: 'gray',
	},
])

// 处理时间周期变化
const handlePeriodChange = () => {
	emit('period-change', selectedPeriod.value)
	// 这里可以根据选择的时间周期更新数据
	updateDataByPeriod(selectedPeriod.value)
}

// 根据时间周期更新数据
const updateDataByPeriod = (period: string) => {
	// 这里可以实现根据不同时间周期加载不同的数据
	console.log('更新数据周期:', period)
}
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
