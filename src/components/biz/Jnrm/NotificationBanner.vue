<template>
	<div
		ref="containerRef"
		class="notification-container h-10 overflow-hidden border border-white/10 rounded-lg bg-black bg-opacity-20 backdrop-blur-sm"
	>
		<!-- 如果有公告数据，显示真实内容 -->
		<div v-if="announcements.length > 0" class="marquee" :class="{ 'marquee--paused': isPaused }">
			<!-- 第一份内容 -->
			<div class="marquee__content">
				<div
					v-for="announcement in announcements"
					:key="`first-${announcement.id}`"
					class="notification-item flex flex-shrink-0 items-center px-4 py-2 text-sm"
				>
					<div class="flex items-center">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="mr-2 h-5 w-5"
							:class="getIconColorClass(announcement.type)"
							viewBox="0 0 20 20"
							fill="currentColor"
						>
							<!-- 重要通知图标 -->
							<path
								v-if="announcement.type === AnnouncementType.IMPORTANT"
								fill-rule="evenodd"
								d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
								clip-rule="evenodd"
							/>
							<!-- 信息图标 (默认) -->
							<path
								v-else
								fill-rule="evenodd"
								d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
								clip-rule="evenodd"
							/>
						</svg>
						<span>{{ announcement.content }}</span>
					</div>
				</div>
			</div>

			<!-- 第二份内容（重复，用于无缝循环） -->
			<div class="marquee__content" aria-hidden="true">
				<div
					v-for="announcement in announcements"
					:key="`second-${announcement.id}`"
					class="notification-item flex flex-shrink-0 items-center px-4 py-2 text-sm"
				>
					<div class="flex items-center">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="mr-2 h-5 w-5"
							:class="getIconColorClass(announcement.type)"
							viewBox="0 0 20 20"
							fill="currentColor"
						>
							<!-- 重要通知图标 -->
							<path
								v-if="announcement.type === AnnouncementType.IMPORTANT"
								fill-rule="evenodd"
								d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
								clip-rule="evenodd"
							/>
							<!-- 信息图标 (默认) -->
							<path
								v-else
								fill-rule="evenodd"
								d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
								clip-rule="evenodd"
							/>
						</svg>
						<span>{{ announcement.content }}</span>
					</div>
				</div>
			</div>
		</div>

		<!-- 如果没有公告，显示测试内容 -->
		<div v-else class="marquee">
			<div class="marquee__content">
				<div class="notification-item flex flex-shrink-0 items-center px-4 py-2 text-sm">
					<div class="flex items-center">
						<svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
							<path
								fill-rule="evenodd"
								d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
								clip-rule="evenodd"
							/>
						</svg>
						<span>测试通知：这是一个测试通知，用于验证滚动效果是否正常工作。</span>
					</div>
				</div>
			</div>
			<div class="marquee__content" aria-hidden="true">
				<div class="notification-item flex flex-shrink-0 items-center px-4 py-2 text-sm">
					<div class="flex items-center">
						<svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
							<path
								fill-rule="evenodd"
								d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
								clip-rule="evenodd"
							/>
						</svg>
						<span>测试通知：这是一个测试通知，用于验证滚动效果是否正常工作。</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import useAnnouncementStore from '@/stores/useAnnouncementStore'
import { AnnouncementType } from '@/types/biz/biztypes'

// 通知横幅组件 - 显示公告信息
interface Props {
	autoInit?: boolean // 是否自动初始化公告
	speed?: number // 滚动速度（秒）
	pauseOnHover?: boolean // 鼠标悬停时是否暂停
}

const props = withDefaults(defineProps<Props>(), {
	autoInit: true,
	speed: 40, // 40秒完成一次循环
	pauseOnHover: true,
})

// 使用公告 store
const announcementStore = useAnnouncementStore()
const { list: announcements } = storeToRefs(announcementStore)

// 暂停状态
const isPaused = ref(false)
const containerRef = ref<HTMLElement | null>(null)

// 根据公告类型获取图标颜色类
const getIconColorClass = (type: AnnouncementType) => {
	const colorMap = {
		[AnnouncementType.IMPORTANT]: 'text-red-500',
		[AnnouncementType.INFO]: 'text-blue-500',
	}
	return colorMap[type] || 'text-blue-500'
}

// 鼠标悬停事件
const handleMouseEnter = () => {
	if (props.pauseOnHover) {
		isPaused.value = true
	}
}

const handleMouseLeave = () => {
	if (props.pauseOnHover) {
		isPaused.value = false
	}
}

// 组件挂载时初始化公告
onMounted(async () => {
	if (props.autoInit) {
		// 获取公告列表
		await announcementStore.getList()
		console.log('公告数据:', announcements.value)
	}

	// 添加鼠标事件监听
	if (containerRef.value && props.pauseOnHover) {
		containerRef.value.addEventListener('mouseenter', handleMouseEnter)
		containerRef.value.addEventListener('mouseleave', handleMouseLeave)
	}
})

// 组件卸载时清理
onUnmounted(() => {
	if (containerRef.value && props.pauseOnHover) {
		containerRef.value.removeEventListener('mouseenter', handleMouseEnter)
		containerRef.value.removeEventListener('mouseleave', handleMouseLeave)
	}
})
</script>

<style scoped>
.marquee {
	--gap: 0rem;
	display: flex;
	overflow: hidden;
	user-select: none;
	gap: var(--gap);
}

.marquee__content {
	flex-shrink: 0;
	display: flex;
	justify-content: flex-start;
	min-width: 100%;
	gap: var(--gap);
	animation: scroll 50s linear infinite;
}

.marquee--paused .marquee__content {
	animation-play-state: paused;
}

@keyframes scroll {
	from {
		transform: translateX(0);
	}
	to {
		transform: translateX(calc(-100% - var(--gap)));
	}
}

/* 支持用户的减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
	.marquee__content {
		animation-duration: 60s; /* 大幅减慢动画速度 */
	}
}

/* 调试：确保动画正在运行 */
.marquee__content {
	/* 添加边框来可视化内容区域 */
	/* border: 1px solid red; */
}
</style>
