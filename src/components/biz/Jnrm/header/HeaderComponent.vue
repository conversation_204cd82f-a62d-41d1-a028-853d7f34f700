<template>
	<header class="glass mb-2 flex items-center justify-between rounded-lg p-2">
		<LogoComponent />
		<TitleComponent :title="title" />
		<div class="w-1/4 flex items-center justify-end space-x-3">
			<WeatherComponent :temperature="weather.temperature" :weather="weather.condition" />
			<!-- 统计看板按钮 -->
			<button
				class="flex items-center whitespace-nowrap rounded-lg bg-gray-800 bg-opacity-40 px-3 py-1.5 transition-all duration-200 hover:bg-opacity-60"
				@click="openStatsModal"
			>
				<div class="i-carbon:analytics mr-1.5 h-5 w-5 text-blue-400"></div>
				<span class="text-white text-sm">统计</span>
			</button>
			<DateTimeLocationComponent :location="location" />
			<UserInfoComponent :user-name="user.name" :user-role="user.role" />
		</div>
	</header>

	<!-- 统计看板弹窗 -->
	<StatsModal ref="statsModalRef" />
</template>

<script setup lang="ts">
import LogoComponent from './LogoComponent.vue'
import TitleComponent from './TitleComponent.vue'
import WeatherComponent from './WeatherComponent.vue'
import DateTimeLocationComponent from './DateTimeLocationComponent.vue'
import UserInfoComponent from './UserInfoComponent.vue'
import StatsModal from './StatsModal.vue'

// 头部组件 - 整合所有头部子组件
interface Props {
	title?: string
	location?: string
	weather?: {
		temperature: number
		condition: string
	}
	user?: {
		name: string
		role: string
	}
}

withDefaults(defineProps<Props>(), {
	location: '山西太原',
	weather: () => ({ temperature: 24, condition: '晴' }),
	user: () => ({ name: '王工程师', role: '调度主管' }),
})

// 统计弹窗引用
const statsModalRef = ref()

// 打开统计弹窗
const openStatsModal = () => {
	statsModalRef.value?.open()
}
</script>

<style scoped>
/* 头部组件样式 */
</style>
