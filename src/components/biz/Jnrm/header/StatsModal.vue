<template>
	<XModal
		v-model="visible"
		title="数据统计中心"
		container-class="!bg-gray-900 !text-white !max-h-[95vh] !w-[95vw]"
		title-class="!text-white !border-primary/40"
		close-on-overlay-click
	>
		<div class="h-84vh flex flex-col overflow-hidden">
			<!-- 顶部切换 -->
			<div class="mb-2 flex items-center justify-between">
				<TabSwitcher
					v-model="activeTab"
					:tabs="[
						{ key: 'orders', label: '运单列表' },
						{ key: 'alerts', label: '告警列表' },
						{ key: 'stats', label: '统计分析' },
					]"
				/>
				<div class="flex items-center gap-base">
					<div>{{ shortcutLabel }}</div>
					<XDateTimePicker
						v-model="orderQueryModel.startTime"
						v-model:shortcut-label="shortcutLabel"
						:hfull="false"
						show-shortcuts
						range
						class="!w-20rem"
						:clearable="false"
						@confirm="orderTableBoardRef?.handleRun()"
					/>
				</div>
			</div>
			<div v-show="activeTab === 'orders'" class="min-h-0 flex-1">
				<OrderTableBoard v-if="orderQueryModel.startTime" ref="orderTableBoardRef" :params="orderQueryModel" />
			</div>
			<div v-show="activeTab === 'alerts'" class="min-h-0 flex-1">
				<AlertTableBoard v-if="orderQueryModel.startTime" ref="orderTableBoardRef" :params="orderQueryModel" />
			</div>
			<!-- 内容区域 -->
			<div v-show="activeTab === 'stats'" class="grid grid-rows-2 min-h-0 flex-1 gap-base">
				<OrderAnalysisSection v-if="activeTab === 'stats'" :params="orderQueryModel" />
				<AlertAnalysisSection v-if="activeTab === 'stats'" />
			</div>
		</div>
	</XModal>
</template>

<script setup lang="ts">
import OrderAnalysisSection from '@/components/biz/Jnrm/stats/OrderAnalysisSection.vue'
import AlertAnalysisSection from '@/components/biz/Jnrm/stats/AlertAnalysisSection.vue'
import OrderTableBoard from '@/components/biz/Jnrm/stats/OrderTableBoard.vue'
import TabSwitcher from '@/components/biz/Jnrm/common/TabSwitcher.vue'
import AlertTableBoard from '~/components/biz/Jnrm/stats/AlertTableBoard.vue'
const orderQueryModel = ref({
	startTime: null,
})
const shortcutLabel = ref('今天')
const orderTableBoardRef = ref()
// 弹窗显示状态
const visible = ref(false)
const activeTab = ref<'stats' | 'orders'>('orders')

// 打开弹窗，仅触发数据加载，实际图表与统计来自子组件
const alertStore = useAlertStore()
const open = () => {
	visible.value = true
	alertStore.getList()
}

// 监听标签切换，当切换到统计分析时触发图表重新渲染
watch(activeTab, (newTab) => {
	if (newTab === 'stats') {
		// 延迟一点时间确保DOM已更新
		nextTick(() => {
			// 触发窗口resize事件，让图表重新计算尺寸
			window.dispatchEvent(new Event('resize'))
		})
	}
})

defineExpose({ open })
</script>

<style scoped>
.echarts {
	width: 100% !important;
	height: 100% !important;
}
</style>
