<template>
	<div class="whitespace-nowrap text-right">
		<p class="text-sm">
			<span class="mr-1 text-white">{{ currentDate }}</span>
			<span class="text-white">{{ currentTime }}</span>
		</p>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 日期时间和位置组件 - 显示当前日期时间和位置信息
interface Props {
	location?: string
}

withDefaults(defineProps<Props>(), {
	location: '山西太原',
})

const currentDate = ref('')
const currentTime = ref('')
let timer: number | null = null

const updateDateTime = () => {
	const now = new Date()
	const year = now.getFullYear()
	const month = String(now.getMonth() + 1).padStart(2, '0')
	const day = String(now.getDate()).padStart(2, '0')
	const hours = String(now.getHours()).padStart(2, '0')
	const minutes = String(now.getMinutes()).padStart(2, '0')
	const seconds = String(now.getSeconds()).padStart(2, '0')

	currentDate.value = `${year}-${month}-${day}`
	currentTime.value = `${hours}:${minutes}:${seconds}`
}

onMounted(() => {
	updateDateTime()
	timer = window.setInterval(updateDateTime, 1000)
})

onUnmounted(() => {
	if (timer) {
		clearInterval(timer)
	}
})
</script>

<style scoped>
/* 日期时间和位置组件样式 */
</style>
