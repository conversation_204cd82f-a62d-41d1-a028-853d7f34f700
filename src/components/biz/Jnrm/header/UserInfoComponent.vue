<template>
	<x-popup trigger="hover" placement="bottom-start">
		<div class="flex items-center">
			<div class="mr-2 h-8 w-8 flex items-center justify-center rounded-full bg-gray-700">
				<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
					<path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
				</svg>
			</div>
			<div>
				<p class="whitespace-nowrap font-semibold text-sm">{{ userStore.userinfo?.nickname }}</p>
				<!--			<p class="text-gray-400 text-xs">{{ userStore.userinfo?.nickname }}</p>-->
			</div>
		</div>
		<template #content>
			<div class="rounded-md bg-primaryBg p-sm-base">
				<div class="cursor-pointer color-white p-xxs-xs hover:color-primary" @click="userStore.logout(userStore.platform === 'web')">退出登录</div>
				<div class="cursor-pointer color-white p-xxs-xs hover:color-primary" @click="handleGoAdmin">运维后台</div>
			</div>
		</template>
	</x-popup>
</template>

<script setup lang="ts">
const userStore = useUserStore()
const handleGoAdmin = () => {
	window.open(`${import.meta.env.VITE_ADMIN_WEB_URL}?token=${userStore.token}`)
}
</script>

<style scoped>
/* 用户信息组件样式 */
</style>
