<template>
  <div>
    <h4 class="text-sm font-medium mb-2">异常事件分布</h4>
    <div class="bg-gray-800 bg-opacity-30 rounded-lg p-3">
      <div 
        v-for="(anomaly, index) in anomalies" 
        :key="index"
        :class="['flex items-center', { 'mb-2': index < anomalies.length - 1 }]"
      >
        <div 
          :class="[
            'w-3 h-3 rounded-full mr-2',
            getColorClass(anomaly.color)
          ]"
        ></div>
        <div class="flex-1">
          <div class="flex justify-between mb-1">
            <span class="text-xs">{{ anomaly.name }}</span>
            <span class="text-xs">{{ anomaly.percentage }}%</span>
          </div>
          <div class="w-full bg-gray-700 rounded-full h-1">
            <div 
              :class="[
                'h-1 rounded-full transition-all duration-500 ease-out',
                getColorClass(anomaly.color)
              ]"
              :style="{ width: anomaly.percentage + '%' }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface AnomalyData {
  name: string
  percentage: number
  color: 'danger' | 'warning' | 'purple' | 'gray'
}

interface Props {
  data?: AnomalyData[]
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [
    {
      name: '铅封异常',
      percentage: 28,
      color: 'danger'
    },
    {
      name: '异常停留',
      percentage: 42,
      color: 'warning'
    },
    {
      name: '路线偏离',
      percentage: 15,
      color: 'purple'
    },
    {
      name: '其他异常',
      percentage: 15,
      color: 'gray'
    }
  ]
})

const anomalies = computed(() => props.data)

const getColorClass = (color: string) => {
  switch (color) {
    case 'danger':
      return 'bg-danger'
    case 'warning':
      return 'bg-warning'
    case 'purple':
      return 'bg-purple-500'
    case 'gray':
      return 'bg-gray-500'
    default:
      return 'bg-gray-500'
  }
}
</script>

<style scoped>
/* 可以添加自定义动画效果 */
</style>