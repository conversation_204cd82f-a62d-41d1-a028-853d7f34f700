<template>
  <div class="text-center p-2 rounded-lg bg-gray-800 bg-opacity-30">
    <p class="text-xs text-gray-400">{{ name }}</p>
    <p class="text-xl font-bold" :class="numberColorClass">{{ value }}</p>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
// defineProps is a compiler macro and no longer needs to be imported

interface Props {
  name: string
  value: string | number
  color?: 'default' | 'success' | 'warning' | 'danger' | 'info'
}

const props = withDefaults(defineProps<Props>(), {
  color: 'default'
})

const numberColorClass = computed(() => {
  const colorMap = {
    default: '',
    success: 'text-success',
    warning: 'text-warning', 
    danger: 'text-danger',
    info: 'text-info'
  }
  return colorMap[props.color]
})
</script>