<template>
  <div class="mb-4">
    <h4 class="text-sm font-medium mb-2">运输时段分布</h4>
    <div class="relative pt-1">
      <div class="w-full h-16 flex gap-0.5">
        <div 
          v-for="(period, index) in timePeriods" 
          :key="index"
          :style="{ height: period.percentage + '%' }"
          class="flex-1 bg-secondary bg-opacity-20 rounded-sm relative"
        >
          <div 
            class="absolute bottom-0 left-0 right-0 bg-secondary rounded-sm transition-all duration-300 hover:opacity-80"
            :style="{ height: period.percentage + '%' }"
          ></div>
        </div>
      </div>
      <div class="flex justify-between text-xs text-gray-400 mt-1">
        <span v-for="label in timeLabels" :key="label">{{ label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface TimePeriodData {
  percentage: number
  hour: number
}

interface Props {
  data?: TimePeriodData[]
  labels?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [
    { percentage: 30, hour: 0 },
    { percentage: 40, hour: 3 },
    { percentage: 60, hour: 6 },
    { percentage: 75, hour: 9 },
    { percentage: 90, hour: 12 },
    { percentage: 65, hour: 15 },
    { percentage: 45, hour: 18 },
    { percentage: 30, hour: 21 }
  ],
  labels: () => ['00:00', '06:00', '12:00', '18:00', '24:00']
})

const timePeriods = computed(() => props.data)
const timeLabels = computed(() => props.labels)
</script>

<style scoped>
/* 自定义样式可以在这里添加 */
</style>