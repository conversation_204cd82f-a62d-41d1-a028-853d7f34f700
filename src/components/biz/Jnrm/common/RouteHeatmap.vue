<template>
  <div class="mb-4">
    <h4 class="text-sm font-medium mb-2">路线热力分布</h4>
    <div class="bg-gray-800 bg-opacity-30 rounded-lg p-3 relative">
      <div class="w-full chart-container flex items-end justify-between">
        <div 
          v-for="(route, index) in routes" 
          :key="index"
          :style="{ height: route.percentage + '%' }"
          :class="[
            'chart-bar w-1/12 rounded-t-sm',
            getBarColor(route.status)
          ]"
        ></div>
      </div>
      <div class="w-full flex justify-between text-xs text-gray-400 mt-1">
        <span v-for="label in routeLabels" :key="label">{{ label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface RouteData {
  percentage: number
  status: 'normal' | 'warning' | 'danger'
}

interface Props {
  data?: RouteData[]
  labels?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [
    { percentage: 60, status: 'normal' },
    { percentage: 85, status: 'normal' },
    { percentage: 40, status: 'normal' },
    { percentage: 95, status: 'danger' },
    { percentage: 70, status: 'normal' },
    { percentage: 50, status: 'normal' },
    { percentage: 30, status: 'normal' },
    { percentage: 45, status: 'normal' },
    { percentage: 75, status: 'normal' },
    { percentage: 90, status: 'warning' },
    { percentage: 65, status: 'normal' },
    { percentage: 55, status: 'normal' }
  ],
  labels: () => ['路线A', '路线F', '路线L']
})

const routes = computed(() => props.data)
const routeLabels = computed(() => props.labels)

const getBarColor = (status: string) => {
  switch (status) {
    case 'danger':
      return 'bg-red-500'
    case 'warning':
      return 'bg-yellow-500'
    default:
      return 'bg-blue-500'
  }
}
</script>

<style scoped>
.chart-container {
  height: 80px;
}

.chart-bar {
  transition: all 0.3s ease;
}

.chart-bar:hover {
  opacity: 0.8;
}
</style>