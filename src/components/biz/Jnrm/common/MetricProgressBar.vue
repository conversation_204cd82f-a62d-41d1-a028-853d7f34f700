<template>
	<div class="space-y-2">
		<div class="flex justify-between items-center">
			<p class="text-sm">{{ name }}</p>
			<div class="flex items-center">
				<span class="mr-1 font-medium text-sm">{{ value }}</span>
				<component :is="trendIcon" class="h-4 w-4" :class="trendColorClass" />
			</div>
		</div>
		<div class="flex items-center space-x-2">
			<div class="flex-1">
				<div class="h-2 w-full rounded-full bg-gray-700">
					<div class="h-2 rounded-full transition-all duration-500" :class="progressColorClass" :style="{ width: percentage + '%' }"></div>
				</div>
			</div>
			<span class="text-gray-400 text-sm whitespace-nowrap">{{ comparison }}</span>
		</div>
	</div>
</template>

<script setup lang="ts">
interface Props {
	name: string
	value: string
	percentage: number
	comparison: string
	trend: 'up' | 'down'
	color?: 'success' | 'warning' | 'danger' | 'info'
}

const props = withDefaults(defineProps<Props>(), {
	color: 'success',
})

const progressColorClass = computed(() => {
	const colorMap = {
		success: 'bg-success',
		warning: 'bg-warning',
		danger: 'bg-danger',
		info: 'bg-info',
	}
	return colorMap[props.color]
})

const trendColorClass = computed(() => {
	const colorMap = {
		success: 'text-success',
		warning: 'text-warning',
		danger: 'text-danger',
		info: 'text-info',
	}
	return colorMap[props.color]
})

const trendIcon = computed(() => {
	return defineComponent({
		render() {
			if (props.trend === 'up') {
				return h(
					'svg',
					{
						xmlns: 'http://www.w3.org/2000/svg',
						viewBox: '0 0 20 20',
						fill: 'currentColor',
					},
					[
						h('path', {
							'fill-rule': 'evenodd',
							d: 'M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z',
							'clip-rule': 'evenodd',
						}),
					],
				)
			} else {
				return h(
					'svg',
					{
						xmlns: 'http://www.w3.org/2000/svg',
						viewBox: '0 0 20 20',
						fill: 'currentColor',
					},
					[
						h('path', {
							'fill-rule': 'evenodd',
							d: 'M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z',
							'clip-rule': 'evenodd',
						}),
					],
				)
			}
		},
	})
})
</script>

