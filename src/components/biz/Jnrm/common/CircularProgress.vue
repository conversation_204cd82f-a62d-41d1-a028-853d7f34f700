<template>
	<div class="flex items-center justify-center">
		<div class="relative" :style="{ width: size + 'px', height: size + 'px' }">
			<svg class="h-full w-full" viewBox="0 0 120 120">
				<!-- 底环 -->
				<circle cx="60" cy="60" r="50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="10" />
				<!-- 进度环 -->
				<circle
					class="progress-ring-circle"
					cx="60"
					cy="60"
					r="50"
					fill="none"
					:stroke="color"
					stroke-width="10"
					stroke-dasharray="314"
					:stroke-dashoffset="314 * (1 - percentage / 100)"
					transform="rotate(-90 60 60)"
				/>
			</svg>
			<div class="absolute inset-0 flex flex-col items-center justify-center">
				<span class="font-bold text-2xl">{{ percentage }}%</span>
				<span class="text-gray-300 text-sm">{{ name }}</span>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
// defineProps is a compiler macro and no longer needs to be imported

interface Props {
	percentage: number
	color: string
	name: string
	size?: number
}

withDefaults(defineProps<Props>(), {
	size: 128,
})
</script>

<style scoped>
.progress-ring-circle {
	transition: stroke-dashoffset 0.5s ease-in-out;
}
</style>
