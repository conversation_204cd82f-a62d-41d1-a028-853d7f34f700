<template>
  <div class="bg-gray-800 bg-opacity-30 rounded-lg p-2">
    <div class="flex justify-between items-center">
      <div class="flex items-center">
        <div class="h-2 w-2 rounded-full mr-1" :class="statusColorClass"></div>
        <span class="text-sm font-medium">{{ name }}</span>
      </div>
      <div class="flex space-x-3">
        <span class="text-sm" :class="statusTextColorClass">{{ currentValue }}</span>
        <span class="text-sm text-gray-300">{{ targetValue }}</span>
      </div>
    </div>
    <div class="mt-1.5">
      <div class="w-full bg-gray-700 rounded-full h-1.5">
        <div 
          class="h-1.5 rounded-full transition-all duration-500" 
          :class="statusColorClass.replace('bg-', 'bg-')"
          :style="{ width: percentage + '%' }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
// defineProps is a compiler macro and no longer needs to be imported

interface Props {
  name: string
  currentValue: string
  targetValue: string
  percentage: number
  status: 'success' | 'warning' | 'danger' | 'primary' | 'info'
}

const props = defineProps<Props>()

const statusColorClass = computed(() => {
  const colorMap = {
    success: 'bg-success',
    warning: 'bg-warning',
    danger: 'bg-danger',
    primary: 'bg-primary',
    info: 'bg-info'
  }
  return colorMap[props.status]
})

const statusTextColorClass = computed(() => {
  const colorMap = {
    success: 'text-success',
    warning: 'text-warning',
    danger: 'text-danger',
    primary: 'text-primary',
    info: 'text-info'
  }
  return colorMap[props.status]
})
</script>

<style scoped>
/* 组件样式已通过Tailwind CSS类处理 */
</style>