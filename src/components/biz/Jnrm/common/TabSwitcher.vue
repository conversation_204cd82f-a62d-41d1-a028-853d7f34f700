<template>
	<div class="seg-container inline-flex items-center self-start gap-1 border rounded-full px-1 py-1" :style="{ width: 'fit-content' }">
		<button v-for="tab in tabs" :key="tab.key" class="seg-btn" :class="{ active: activeTab === tab.key }" @click="switchTab(tab.key)">
			{{ tab.label }}
		</button>
	</div>
</template>

<script setup lang="ts">
// defineProps is a compiler macro and no longer needs to be imported

interface Tab {
	key: string
	label: string
}

interface Props {
	tabs: Tab[]
	defaultTab?: string
	modelValue?: string
}

interface Emits {
	(e: 'tab-change', tabKey: string): void
	(e: 'update:modelValue', tabKey: string): void
}

const props = withDefaults(defineProps<Props>(), {
	defaultTab: '',
	modelValue: '',
})

const emit = defineEmits<Emits>()

const activeTab = ref(props.modelValue || props.defaultTab || props.tabs[0]?.key || '')

const switchTab = (tabKey: string) => {
	activeTab.value = tabKey
	emit('tab-change', tabKey)
	emit('update:modelValue', tabKey)
}

// 监听外部modelValue变化
watch(
	() => props.modelValue,
	(newValue) => {
		if (newValue && newValue !== activeTab.value) {
			activeTab.value = newValue
		}
	},
)
</script>

<style scoped>
/* 大气精致的分段 Tab 切换器 */
.seg-container {
	/* 使用主题主色做渐变与高光，附带兜底色值 */
	--seg-primary-400: var(--color-primary-400, #60a5fa);
	--seg-primary-500: var(--color-primary-500, #3b82f6);
	--seg-primary-rgb: var(--color-primary-400-rgb, 96, 165, 250);
	--seg-bg: rgba(17, 25, 40, 0.55);
	--seg-border: rgba(255, 255, 255, 0.08);
	--seg-text: #b7c3d6;
	--seg-text-active: #ffffff;

	position: relative;
	background: var(--seg-bg);
	border: 1px solid var(--seg-border);
	backdrop-filter: blur(8px) saturate(120%);
	box-shadow:
		inset 0 1px 0 rgba(255, 255, 255, 0.06),
		0 8px 30px rgba(0, 0, 0, 0.25);
}

/* 渐变外发光边框（不占位） */
.seg-container::before {
	content: '';
	position: absolute;
	inset: -1px; /* 覆盖边框，形成环形高光 */
	border-radius: inherit;
	padding: 1px; /* 用遮罩制造 1px 的描边效果 */
	background: linear-gradient(135deg, rgba(var(--seg-primary-rgb), 0.45), rgba(255, 255, 255, 0.06));
	-webkit-mask:
		linear-gradient(#000 0 0) content-box,
		linear-gradient(#000 0 0);
	-webkit-mask-composite: xor;
	mask-composite: exclude;
	pointer-events: none;
	opacity: 0.9;
}

.seg-btn {
	position: relative;
	border-radius: 9999px;
	padding: 6px 14px; /* 更大的内边距，显得大气 */
	font-size: 13px;
	line-height: 1; /* 垂直居中更紧致 */
	color: var(--seg-text);
	transition: all 0.25s ease;
	border: 1px solid transparent;
	background: transparent;
	letter-spacing: 0.2px;
}

.seg-btn:hover {
	color: #e6ecf5;
	background: rgba(255, 255, 255, 0.06);
}

.seg-btn.active {
	color: var(--seg-text-active);
	background: linear-gradient(135deg, var(--seg-primary-400) 0%, var(--seg-primary-500) 100%);
	box-shadow:
		0 0 0 1px rgba(255, 255, 255, 0.08) inset,
		0 8px 18px rgba(var(--seg-primary-rgb), 0.35),
		0 2px 6px rgba(0, 0, 0, 0.25);
}

/* 活动态顶部高光，提升质感 */
.seg-btn.active::before {
	content: '';
	position: absolute;
	left: 14%;
	right: 14%;
	top: 2px;
	height: 38%;
	border-radius: inherit;
	background: linear-gradient(to bottom, rgba(255, 255, 255, 0.35), rgba(255, 255, 255, 0.02));
	filter: blur(0.2px);
	pointer-events: none;
}

/* 细微的内阴影，增加材质层次 */
.seg-btn::after {
	content: '';
	position: absolute;
	inset: 0;
	border-radius: inherit;
	pointer-events: none;
	box-shadow:
		0 1px 0 rgba(255, 255, 255, 0.06) inset,
		0 -1px 0 rgba(0, 0, 0, 0.25) inset;
}
</style>
