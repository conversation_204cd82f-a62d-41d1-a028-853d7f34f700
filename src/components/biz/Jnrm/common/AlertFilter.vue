<template>
	<div class="mb-3 flex items-center justify-between gap-base">
		<div class="flex space-x-1">
			<button
				v-for="filter in filters"
				:key="filter.key"
				class="whitespace-nowrap border rounded-full px-2 py-1 transition-all duration-200 text-sm"
				:class="{
					'bg-danger bg-opacity-30 border-danger': filter.key === '严重' && queryModel?.level === filter.key,
					'bg-warning bg-opacity-20 border-warning': filter.key === '警告' && queryModel?.level === filter.key,
					'bg-gray-600 bg-opacity-20 border-gray-600': filter.key === null && queryModel?.level === filter.key,
					'bg-transparent border-gray-600 hover:bg-gray-600 hover:bg-opacity-10': queryModel?.level !== filter.key,
				}"
				@click="
					() => {
						queryModel.level = filter.key
						emit('change')
					}
				"
			>
				{{ filter.label }}
			</button>
		</div>

	</div>
</template>

<script setup lang="ts">
const queryModel = defineModel({ default: () => ({}) })
const emit = defineEmits(['change'])
const userStore = useUserStore()

const filters = [
	{ key: null, label: '全部' },
	{ key: '严重', label: '严重' },
	{ key: '警告', label: '警告' },
]
</script>
