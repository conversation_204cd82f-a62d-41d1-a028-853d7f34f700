<template>
	<div class="grid grid-cols-3 mb-4 gap-2">
		<div
			v-for="stat in stats"
			:key="stat.type"
			class="rounded-lg p-2 text-center transition-all duration-200 cursor-pointer"
			:class="{
				// 选中状态的样式（使用 box-shadow 不占位置）
				'bg-primary/20': selectedStat === stat.type,
				// 未选中状态的样式
				'bg-gray-800 bg-opacity-30 hover:bg-opacity-50': selectedStat !== stat.type
			}"
			:style="{
				boxShadow: selectedStat === stat.type ? '0 0 0 2px rgba(var(--color-primary), 0.4)' : 'none'
			}"
			@click="$emit('stat-click', stat.type)"
		>
			<p class="text-gray-400 text-sm">{{ stat.label }}</p>
			<p
				class="font-bold text-xl"
				:class="{
					'text-danger': stat?.level === '严重',
					'text-warning': stat?.level === '警告',
					'text-info': stat?.level === null,
				}"
			>
				{{ stat.count }}
			</p>
		</div>
	</div>
</template>

<script setup lang="ts">
defineProps({
	stats: { type: Array, default: () => [] },
	selectedStat: { type: String, default: null },
})
</script>
