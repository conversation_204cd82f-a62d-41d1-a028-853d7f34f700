<template>
  <div class="mb-2">
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center">
        <div 
          class="w-3 h-3 rounded-full mr-2" 
          :style="{ backgroundColor: color }"
        ></div>
        <span class="text-sm">{{ name }}</span>
      </div>
      <span class="text-sm font-medium">{{ percentage }}%</span>
    </div>
    <div class="w-full bg-gray-700 rounded-full h-2">
      <div 
        class="h-2 rounded-full transition-all duration-300" 
        :style="{ 
          backgroundColor: color, 
          width: percentage + '%' 
        }"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
// defineProps is a compiler macro and no longer needs to be imported

interface Props {
  name: string
  color: string
  percentage: number
}

defineProps<Props>()
</script>