<template>
	<div class="flex flex-col border border-blue-700/40 rounded-lg from-blue-900/30 to-blue-800/20 bg-gradient-to-br p-4 shadow-xl backdrop-blur-sm">
		<div class="mb-3 flex items-center justify-between border-red-600/30 pb-3 border-b">
			<div class="flex items-center">
				<div class="mr-3 border border-blue-500/30 rounded-lg bg-blue-600/20 p-2">
					<div class="i-carbon:warning-alt text-blue-400 text-lg"></div>
				</div>
				<div>
					<h2 class="text-white font-bold text-lg">告警数据分析</h2>
				</div>
			</div>
		</div>

		<div class="min-h-0 flex flex-1 flex-col">
			<div class="grid grid-cols-4 mb-4 gap-4">
				<StatsCard icon="i-carbon:warning-alt" label="总告警数" :value="totalAlerts" tag-text="总计" />
				<StatsCard icon="i-carbon:security" label="铅封告警" :value="sealAlerts" tag-text="重要" />
				<StatsCard icon="i-carbon:in-progress" label="未处理告警" :value="unhandledAlerts" tag-text="未处理" />
				<StatsCard icon="i-carbon:checkmark-filled" label="已处理" :value="handledAlerts" tag-text="完成" theme="green" />
			</div>

			<div class="grid grid-cols-2 min-h-0 flex-1 gap-4">
				<DailyAlertTrendChart />
				<VehicleAlertRankingChart />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import StatsCard from './StatsCard.vue'
import DailyAlertTrendChart from './charts/DailyAlertTrendChart.vue'
import VehicleAlertRankingChart from './charts/VehicleAlertRankingChart.vue'
import useAlertStore from '@/stores/useAlertStore'

const alertStore = useAlertStore()

const totalAlerts = computed(() => alertStore.list.length)
const sealAlerts = computed(() => alertStore.list.filter((a: any) => a.alertType === '铅封告警').length)
const unhandledAlerts = computed(() => alertStore.list.filter((a: any) => !a.handleStatus).length)
const handledAlerts = computed(() => alertStore.list.filter((a: any) => a.handleStatus).length)
</script>
