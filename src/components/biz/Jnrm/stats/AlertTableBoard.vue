<template>
	<div class="hfull flex flex-col gap-3">
		<!-- 顶部卡片统计 -->
		<div class="grid grid-cols-4 gap-3">
			<StatsCard icon="i-carbon:warning-alt" label="总告警数" :value="computeStatusCount(1)" tag-text="总计" />
			<StatsCard icon="i-carbon:security" label="铅封告警" :value="computeStatusCount(1)" tag-text="重要" />
			<StatsCard icon="i-carbon:in-progress" label="未处理告警" :value="computeStatusCount(1)" tag-text="未处理" />
			<StatsCard icon="i-carbon:checkmark-filled" label="已处理" :value="computeStatusCount(1)" tag-text="完成" theme="green" />
		</div>

		<!-- 表格与分页区域 -->
		<div
			class="min-h-0 flex flex-1 flex-col overflow-hidden border border-blue-700/30 rounded-lg from-blue-900/20 to-blue-800/10 bg-gradient-to-br p-3"
		>
			<!-- 表格 -->
			<div class="min-h-0 flex-1 overflow-hidden">
				<x-table
					v-loading="loading"
					class="!hfull"
					:loading="loading"
					:data="list"
					:columns="columns"
					:ellipsis="true"
					:header-align="'center'"
					:body-align="'center'"
					:max-height="'100%'"
					col-class="p-xxs"
				/>
			</div>
			<!-- 分页 -->
			<div class="mt-3 flex justify-end">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import StatsCard from './StatsCard.vue'
import { useTransportOrderStore, TransportOrderStatus, VehicleOnlineStatus } from '@/stores/useTransportOrderStore'
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
function computeStatusCount(status) {
	return statsData.value?.byStatus?.[status] || 0
}
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	orderNo: null,
	powerPlantId: null,
	departureId: null,
	destinationId: null,
	vehicleId: null,
	driverId: null,
	licensePlate: null,
	driverPhone: null,
	cargoInfo: null,
	cargoWeight: null,
	startTime: null,
	deliveryTime: [],
	estimatedArrivalTime: [],
	actualArrivalTime: [],
	status: null,
	createTime: [],
})

// 监听 computedParams 的变化，同步更新 queryModel
watch(
	() => props.params,
	(newParams) => {
		Object.assign(queryModel, newParams)
	},
	{ deep: true, immediate: true },
)

const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmAlert_APIS.getPage, queryModel)
const { loading: statsLoading, run: statsRun, data: statsData } = xUseRequest(BIZ_JnrmTransportOrder_APIS.getStatsByStatus, queryModel)

onMounted(() => {
	handleRun()
})
// 数据源
const transportOrderStore = useTransportOrderStore()

// 顶部卡片统计

// 列定义（x-table 支持点语法取嵌套字段，formatter 用于转换/格式化）
const columns = [
	{
		label: '告警类型',
		prop: 'alertType',
	},
	{
		label: '告警内容',
		prop: 'alertContent',
	},
	{
		label: '车牌号',
		prop: 'licensePlate',
	},
	{
		label: '处理状态',
		prop: 'handleStatus',
		tagGroupName: 'JNRM告警处理状态',
		tagValueFormat: 'BOOLEAN',
	},
	{
		label: '告警时间',
		prop: 'alertTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	/*	{
		label: '司机名称',
		prop: 'driverName',
	},
	{
		label: '司机手机号码',
		prop: 'driverPhone',
	},*/
	/*	{
		label: '创建时间',
		prop: 'createTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},*/
]

async function handleRun() {
	await nextTick()
	statsRun()
	run()
}

defineExpose({ handleRun })
</script>

<style scoped></style>
