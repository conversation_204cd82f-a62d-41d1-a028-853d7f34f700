<template>
	<div class="flex flex-col border border-blue-700/40 rounded-lg from-blue-900/30 to-blue-800/20 bg-gradient-to-br p-4 shadow-xl backdrop-blur-sm">
		<div class="mb-3 flex items-center justify-between border-blue-600/30 pb-3 border-b">
			<div class="flex items-center">
				<div class="mr-3 border border-blue-500/30 rounded-lg bg-blue-600/20 p-2">
					<div class="i-carbon:delivery-truck text-blue-400 text-lg"></div>
				</div>
				<div>
					<h2 class="text-white font-bold text-lg">运单数据分析</h2>
				</div>
			</div>
		</div>

		<div class="min-h-0 flex flex-1 flex-col">
			<div class="grid grid-cols-4 mb-4 gap-4">
				<StatsCard icon="i-carbon:delivery-truck" label="总运单数" :value="statsData?.total" tag-text="总计" />
				<StatsCard icon="i-carbon:car" label="在途车辆" :value="computeStatusCount(TransportOrderStatus.在途)" tag-text="在途" />
				<StatsCard icon="i-carbon:chart-line" label="完成率" :value="taskCompletionRate + '%'" tag-text="效率" />
				<StatsCard icon="i-carbon:checkmark" label="已送达" :value="computeStatusCount(TransportOrderStatus.已送达)" tag-text="完成" theme="green" />
			</div>

			<div class="grid grid-cols-2 min-h-0 flex-1 gap-4">
				<DailyOrderTrendChart :params="chartParams" />
				<MonthlyOrderComparisonChart :params="chartParams" />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import StatsCard from './StatsCard.vue'
import DailyOrderTrendChart from './charts/DailyOrderTrendChart.vue'
import MonthlyOrderComparisonChart from './charts/MonthlyOrderComparisonChart.vue'
import { TransportOrderStatus, useTransportOrderStore } from '@/stores/useTransportOrderStore'

// 接收父组件传递的查询参数
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})

const transportOrderStore = useTransportOrderStore()

const taskCompletionRate = computed(() => transportOrderStore.taskCompletionRate)

// 传递给图表组件的参数
const chartParams = computed(() => ({
	...props.params,
}))

const { loading: statsLoading, run: statsRun, data: statsData } = xUseRequest(BIZ_JnrmTransportOrder_APIS.getStatsByStatus, chartParams)
function computeStatusCount(status) {
	return statsData.value?.byStatus?.[status] || 0
}
watch(
	chartParams.value,
	() => {
		console.log(111111)
		statsRun()
	},
	{
		immediate: true,
		deep: true,
	},
)
</script>
