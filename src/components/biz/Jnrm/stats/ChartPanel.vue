<template>
	<div class="min-h-0 flex flex-col border border-gray-600/50 rounded-lg bg-gray-800/50 p-3 shadow-lg backdrop-blur-sm">
		<div v-if="title || icon" class="mb-3 flex items-center border-gray-600/30 pb-2 border-b">
			<div v-if="icon" :class="icon" class="mr-2 text-white text-lg"></div>
			<h3 v-if="title" class="text-white font-semibold text-sm">{{ title }}</h3>
		</div>
		<div ref="chartContainer" class="min-h-0 flex-1">
			<VChart
				ref="chartRef"
				:option="option"
				class="h-full w-full"
				:autoresize="true"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { Line<PERSON>hart, Pie<PERSON><PERSON>, BarChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'

// Ensure ECharts modules are registered when this panel is used
use([CanvasRenderer, LineChart, PieChart, BarChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent])

interface Props {
	title?: string
	icon?: string
	option: any
}

const props = defineProps<Props>()

const chartRef = ref()
const chartContainer = ref()

// 监听容器可见性变化，重新调整图表尺寸
const resizeChart = () => {
	if (chartRef.value) {
		nextTick(() => {
			chartRef.value.resize()
		})
	}
}

// 使用 ResizeObserver 监听容器尺寸变化
onMounted(() => {
	if (chartContainer.value) {
		const resizeObserver = new ResizeObserver(() => {
			resizeChart()
		})
		resizeObserver.observe(chartContainer.value)

		// 组件卸载时清理观察器
		onUnmounted(() => {
			resizeObserver.disconnect()
		})
	}

	// 延迟调整图表尺寸，确保容器已完全渲染
	setTimeout(() => {
		resizeChart()
	}, 100)
})

// 监听 option 变化时也调整尺寸
watch(() => props.option, () => {
	nextTick(() => {
		resizeChart()
	})
}, { deep: true })
</script>

<style scoped>
.echarts {
	width: 100% !important;
	height: 100% !important;
}
</style>
