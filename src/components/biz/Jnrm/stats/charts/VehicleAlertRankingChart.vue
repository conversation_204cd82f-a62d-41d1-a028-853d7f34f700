<template>
	<ChartPanel title="车辆告警排名" icon="i-carbon:industry" :option="option" />
</template>

<script setup lang="ts">
import ChartPanel from '../ChartPanel.vue'

const UNIFIED_GRID = { left: '0.5%', right: '0.5%', top: '5%', bottom: '5%', containLabel: true }

const option = computed(() => {
	const vehicles = ['京A12345', '津B67890', '冀C11111', '晋D22222', '蒙E33333']
	const alertCounts = [45, 38, 32, 28, 22]
	return {
		backgroundColor: 'transparent',
		tooltip: { trigger: 'axis', backgroundColor: 'rgba(0, 0, 0, 0.8)', borderColor: '#333', textStyle: { color: '#fff' } },
		grid: UNIFIED_GRID,
		xAxis: {
			type: 'category',
			data: vehicles,
			axisLine: { lineStyle: { color: '#666' } },
			axisLabel: { color: '#ccc', rotate: 30, interval: 0, fontSize: 10 },
		},
		yAxis: {
			type: 'value',
			axisLine: { lineStyle: { color: '#666' } },
			axisLabel: { color: '#ccc', fontSize: 10 },
			splitLine: { lineStyle: { color: '#333' } },
		},
		series: [
			{
				name: '告警数量',
				type: 'bar',
				data: alertCounts,
				itemStyle: {
					color: {
						type: 'linear',
						x: 0,
						y: 0,
						x2: 0,
						y2: 1,
						colorStops: [
							{ offset: 0, color: '#eab308' },
							{ offset: 1, color: '#ca8a04' },
						],
					},
				},
				emphasis: { itemStyle: { color: '#fbbf24' } },
			},
		],
	}
})
</script>
