<template>
	<ChartPanel title="月度运单对比" icon="i-carbon:chart-line-smooth" :option="option" />
</template>

<script setup lang="ts">
import ChartPanel from '../ChartPanel.vue'
import { BIZ_JnrmTransportOrder_APIS } from '@/api/biz/admin/jnrmtransportorderapi'

const UNIFIED_GRID = { left: '0.5%', right: '0.5%', top: '5%', bottom: '5%', containLabel: true }

// 接收父组件传递的查询参数
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})

// 查询参数
const queryModel = reactive({
	...props.params,
})

// 使用 xUseRequest 获取月度对比统计数据
const { loading, run: monthlyStatsRun, data: monthlyStatsData } = xUseRequest(
	() => BIZ_JnrmTransportOrder_APIS.getMonthlyCompareStats(queryModel),
	queryModel,
)

// 安全的请求函数，避免重复请求
const safeMonthlyStatsRun = () => {
	if (!loading.value) {
		monthlyStatsRun()
	}
}

// 监听 props.params 的变化，同步更新 queryModel
watch(
	() => props.params,
	(newParams) => {
		Object.assign(queryModel, newParams)
		// 只有当参数真正变化时才重新请求
		if (Object.keys(newParams).length > 0) {
			// 使用 nextTick 确保响应式更新完成后再请求
			nextTick(() => {
				safeMonthlyStatsRun()
			})
		}
	},
	{ deep: true },
)

// 处理API返回的数据，转换为图表需要的格式
const processedData = computed(() => {
	if (!monthlyStatsData.value?.currentYearData || !monthlyStatsData.value?.previousYearData) {
		return {
			months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
			thisYear: [],
			lastYear: [],
			currentYear: new Date().getFullYear(),
			previousYear: new Date().getFullYear() - 1
		}
	}

	const currentYearData = monthlyStatsData.value.currentYearData
	const previousYearData = monthlyStatsData.value.previousYearData || {}
	const currentYear = monthlyStatsData.value.currentYear || new Date().getFullYear()
	const previousYear = monthlyStatsData.value.previousYear || (new Date().getFullYear() - 1)

	const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
	const thisYear: number[] = []
	const lastYear: number[] = []

	// 处理12个月的数据
	for (let month = 1; month <= 12; month++) {
		const monthStr = month.toString()

		// 计算当年该月的总运单数
		let currentMonthTotal = 0
		if (currentYearData[monthStr]) {
			Object.keys(currentYearData[monthStr]).forEach(status => {
				currentMonthTotal += currentYearData[monthStr][status] || 0
			})
		}
		thisYear.push(currentMonthTotal)

		// 计算去年该月的总运单数
		let previousMonthTotal = 0
		if (previousYearData[monthStr]) {
			Object.keys(previousYearData[monthStr]).forEach(status => {
				previousMonthTotal += previousYearData[monthStr][status] || 0
			})
		}
		lastYear.push(previousMonthTotal)
	}

	return { months, thisYear, lastYear, currentYear, previousYear }
})

const option = computed(() => {
	const { months, thisYear, lastYear, currentYear, previousYear } = processedData.value

	return {
		backgroundColor: 'transparent',
		tooltip: { trigger: 'axis', backgroundColor: 'rgba(0, 0, 0, 0.8)', borderColor: '#333', textStyle: { color: '#fff' } },
		legend: { data: [`${currentYear}年`, `${previousYear}年`], textStyle: { color: '#fff', fontSize: 10 } },
		grid: UNIFIED_GRID,
		xAxis: { type: 'category', data: months, axisLine: { lineStyle: { color: '#666' } }, axisLabel: { color: '#ccc', fontSize: 10 } },
		yAxis: {
			type: 'value',
			axisLine: { lineStyle: { color: '#666' } },
			axisLabel: { color: '#ccc', fontSize: 10 },
			splitLine: { lineStyle: { color: '#333' } },
			minInterval: 1 // 确保y轴只显示整数
		},
		series: [
			{
				name: `${currentYear}年`,
				type: 'line',
				data: thisYear,
				smooth: true,
				lineStyle: { color: '#06b6d4', width: 2 },
				itemStyle: { color: '#06b6d4' },
				areaStyle: {
					color: {
						type: 'linear',
						x: 0,
						y: 0,
						x2: 0,
						y2: 1,
						colorStops: [
							{ offset: 0, color: 'rgba(6, 182, 212, 0.3)' },
							{ offset: 1, color: 'rgba(6, 182, 212, 0.1)' },
						],
					},
				},
			},
			{
				name: `${previousYear}年`,
				type: 'line',
				data: lastYear,
				smooth: true,
				lineStyle: { color: '#8b5cf6', type: 'dashed', width: 2 },
				itemStyle: { color: '#8b5cf6' },
			},
		],
	}
})

// 组件挂载时获取数据
onMounted(() => {
	if (Object.keys(props.params).length > 0) {
		nextTick(() => {
			safeMonthlyStatsRun()
		})
	}
})
</script>
