<template>
	<ChartPanel title="每日告警趋势" icon="i-carbon:chart-line" :option="option" />
</template>

<script setup lang="ts">
import ChartPanel from '../ChartPanel.vue'

import useAlertStore from '@/stores/useAlertStore'
const alertStore = useAlertStore()
const UNIFIED_GRID = { left: '0.5%', right: '0.5%', top: '5%', bottom: '5%', containLabel: true }

const generateDailyData = (days: number = 7) => {
	const dates: string[] = []
	const today = new Date()
	for (let i = days - 1; i >= 0; i--) {
		const date = new Date(today)
		date.setDate(date.getDate() - i)
		dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))
	}
	return dates
}

const option = computed(() => {
	const dates = generateDailyData(1)
	const totalCount = alertStore.list.length
	const handledCount = alertStore.list.filter((a: any) => a.handleStatus).length
	const avgDaily = Math.max(1, Math.floor(totalCount / 7))
	const avgHandled = Math.max(1, Math.floor(handledCount / 7))
	const totalAlerts = Array.from({ length: 7 }, (_, i) => Math.max(1, avgDaily + Math.floor(Math.random() * 10 - 5)))
	const processedAlerts = Array.from({ length: 7 }, (_, i) => Math.max(0, Math.min(totalAlerts[i], avgHandled + Math.floor(Math.random() * 8 - 4))))
	return {
		backgroundColor: 'transparent',
		tooltip: { trigger: 'axis', backgroundColor: 'rgba(0, 0, 0, 0.8)', borderColor: '#333', textStyle: { color: '#fff' } },
		legend: { data: ['总告警', '已处理'], textStyle: { color: '#fff' } },
		grid: UNIFIED_GRID,
		xAxis: { type: 'category', data: dates, axisLine: { lineStyle: { color: '#666' } }, axisLabel: { color: '#ccc' } },
		yAxis: { type: 'value', axisLine: { lineStyle: { color: '#666' } }, axisLabel: { color: '#ccc' }, splitLine: { lineStyle: { color: '#333' } } },
		series: [
			{ name: '总告警', type: 'line', data: totalAlerts, smooth: true, lineStyle: { color: '#ef4444' }, itemStyle: { color: '#ef4444' } },
			{ name: '已处理', type: 'line', data: processedAlerts, smooth: true, lineStyle: { color: '#10b981' }, itemStyle: { color: '#10b981' } },
		],
	}
})
</script>
