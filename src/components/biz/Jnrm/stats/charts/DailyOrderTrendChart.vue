<template>
	<ChartPanel title="每日运单趋势" icon="i-carbon:chart-line" :option="option" />
</template>

<script setup lang="ts">
import ChartPanel from '../ChartPanel.vue'
import { BIZ_JnrmTransportOrder_APIS } from '@/api/biz/admin/jnrmtransportorderapi'

const UNIFIED_GRID = { left: '0.5%', right: '0.5%', top: '5%', bottom: '5%', containLabel: true }

// 接收父组件传递的查询参数
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})

// 查询参数
const queryModel = reactive({
	...props.params,
})

// 使用 xUseRequest 获取每日统计数据
const { loading, run: dailyStatsRun, data: dailyStatsData } = xUseRequest(
	() => BIZ_JnrmTransportOrder_APIS.getDailyStatsByStatus(queryModel),
	queryModel,
)

// 安全的请求函数，避免重复请求
const safeDailyStatsRun = () => {
	if (!loading.value) {
		dailyStatsRun()
	}
}

// 监听 props.params 的变化，同步更新 queryModel
watch(
	() => props.params,
	(newParams) => {
		Object.assign(queryModel, newParams)
		// 只有当参数真正变化时才重新请求
		if (Object.keys(newParams).length > 0) {
			// 使用 nextTick 确保响应式更新完成后再请求
			nextTick(() => {
				safeDailyStatsRun()
			})
		}
	},
	{ deep: true },
)

// 处理API返回的数据，转换为图表需要的格式
const processedData = computed(() => {
	if (!dailyStatsData.value?.dailyStats) {
		return { dates: [], totalOrders: [], completedOrders: [] }
	}

	const dailyStats = dailyStatsData.value.dailyStats
	const dates: string[] = []
	const totalOrders: number[] = []
	const completedOrders: number[] = []

	// 按日期排序并处理数据
	const sortedDates = Object.keys(dailyStats).sort()

	sortedDates.forEach(dateStr => {
		const date = new Date(dateStr)
		dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))

		const dayData = dailyStats[dateStr]
		// 计算总运单数（所有状态的运单数量之和）
		let totalCount = 0
		// 计算已完成运单数（状态为5的运单数量，根据业务逻辑调整）
		let completedCount = 0

		Object.keys(dayData).forEach(status => {
			const count = dayData[status] || 0
			totalCount += count
			// 根据实际业务逻辑，这里假设状态5表示已完成
			// 如果有其他完成状态，可以在这里添加
			if (status === '5') {
				completedCount += count
			}
		})

		totalOrders.push(totalCount)
		completedOrders.push(completedCount)
	})

	return { dates, totalOrders, completedOrders }
})

const option = computed(() => {
	const { dates, totalOrders, completedOrders } = processedData.value

	return {
		backgroundColor: 'transparent',
		tooltip: { trigger: 'axis', backgroundColor: 'rgba(0, 0, 0, 0.8)', borderColor: '#333', textStyle: { color: '#fff' } },
		legend: { data: ['总运单', '已完成'], textStyle: { color: '#fff' } },
		grid: UNIFIED_GRID,
		xAxis: { type: 'category', data: dates, axisLine: { lineStyle: { color: '#666' } }, axisLabel: { color: '#ccc' } },
		yAxis: {
			type: 'value',
			axisLine: { lineStyle: { color: '#666' } },
			axisLabel: { color: '#ccc' },
			splitLine: { lineStyle: { color: '#333' } },
			minInterval: 1 // 确保y轴只显示整数
		},
		series: [
			{ name: '总运单', type: 'line', data: totalOrders, smooth: true, lineStyle: { color: '#3b82f6' }, itemStyle: { color: '#3b82f6' } },
			{ name: '已完成', type: 'line', data: completedOrders, smooth: true, lineStyle: { color: '#10b981' }, itemStyle: { color: '#10b981' } },
		],
	}
})

// 组件挂载时获取数据
onMounted(() => {
	if (Object.keys(props.params).length > 0) {
		nextTick(() => {
			safeDailyStatsRun()
		})
	}
})
</script>
