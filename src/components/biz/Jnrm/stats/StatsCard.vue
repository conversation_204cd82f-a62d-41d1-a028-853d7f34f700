<template>
	<div :class="wrapperClass">
		<div class="mb-2 flex items-center justify-between">
			<div class="flex items-center gap-2">
				<div :class="icon" class="text-lg" :style="{ color: iconColor }"></div>
				<div class="text-xs" :style="{ color: labelColor }">{{ label }}</div>
			</div>
			<div :class="tagClass">{{ tagText }}</div>
		</div>
		<div class="mb-1 text-white font-bold text-xl">{{ value }}</div>
	</div>
</template>

<script setup lang="ts">
interface Props {
	icon: string
	label: string
	value: string | number
	tagText?: string
	theme?: 'blue' | 'green'
}

const props = withDefaults(defineProps<Props>(), {
	tagText: '',
	theme: 'blue',
})

const wrapperClass = computed(() =>
	props.theme === 'green'
		? 'border border-green-500/30 rounded-lg from-green-600/20 to-green-700/10 bg-gradient-to-br p-3 shadow-lg'
		: 'border border-blue-500/30 rounded-lg from-blue-600/20 to-blue-700/10 bg-gradient-to-br p-3 shadow-lg',
)

const tagClass = computed(() =>
	props.theme === 'green'
		? 'rounded bg-green-500/20 px-1.5 py-0.5 text-[10px] text-green-300 text-xs'
		: 'rounded bg-blue-500/20 px-1.5 py-0.5 text-[10px] text-blue-300 text-xs',
)

const iconColor = computed(() => (props.theme === 'green' ? '#34d399' : '#60a5fa'))
const labelColor = computed(() => (props.theme === 'green' ? '#bbf7d0' : '#bfdbfe'))
</script>
