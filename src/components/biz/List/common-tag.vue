<route lang="json">
{
	"meta": {
		"title": "标签系统"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
	searchable: {
		type: Boolean,
		default: true,
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	createTime: [],
	label: undefined,
	groupId: computedParams.value.groupId,
	sort: undefined,
})

const modalRef = ref(null)
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_CommonTag_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id, rowIndex) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_CommonTag_APIS.delete(id, computedParams.value?.groupName).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	list.value.splice(rowIndex, 1)
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '名称',
		prop: 'label',
		width: 150,
	},
	{
		label: '值',
		prop: 'value',
	},
	{
		label: '字符串',
		prop: 'stringValue',
	},
	{
		label: '数值',
		prop: 'numberValue',
	},
	{
		label: 'BOOLEAN',
		prop: 'boolValue',
	},
	{
		label: '操作',
		prop: 'action',
		width: 120,
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsCommonTag ref="modalRef" @success="run()" />
		<BizCardsQuery v-if="searchable">
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="名称" prop="label">
					<XInput v-model="queryModel.label" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="分组id" prop="groupId">
					<XInput v-model="queryModel.groupId" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="排序" prop="sort">
					<XInput v-model="queryModel.sort" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
					<BizButtonsReset @click="reset" />
					<BizButtonsQuery @click="run" />
				</div>
			</BizFormQueryForm>
		</BizCardsQuery>
		<div class="flex items-center">
			<BizButtonsCreate v-if="hasPermission('x:common-tag:create')" class="ml-auto" @click="handleOpenModal('create')" />
		</div>
		<div v-loading="loading" class="mt-sm">
			<BizTablesList :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('x:common-tag:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:common-tag:delete')" @click="handleDelete(row.id, rowIndex)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</div>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
