<script setup lang="ts">
const props = defineProps({
	modelValue: {
		type: Array,
		default: () => []
	}
})

const emit = defineEmits(['update:modelValue'])

const selectedActivities = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value)
})

const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	createTime: undefined,
	sortNo: undefined,
	actUser: undefined,
	docId: undefined,
	actName: undefined,
	actClaim: undefined,
	actInfoName: undefined,
	actInspection: undefined,
	actForm: undefined,
	actRisk: undefined,
	impactLevel: undefined,
	possibility: undefined,
	riskScore: undefined,
	riskLevel: undefined,
	actType: null,
	deptId: null,
})

const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_FxpjSysActivity_APIS.getPage, queryModel)

onMounted(() => {
	run()
})

function handleSelectionChange(selection) {
	// update:selected事件直接传递选中行数组
	selectedActivities.value = selection
}

const columns = [
	{
		label: '控制活动名称',
		prop: 'actName',
	},
	{
		label: '活动编号',
		prop: 'actCode',
	},
	{
		label: '活动类型',
		tagGroupName: 'FXPJ活动类型',
		prop: 'actType',
	},
	{
		label: '所属制度',
		prop: 'sysDoc.docName',
	},
	{
		label: '主责部门',
		prop: 'deptName',
	},
	{
		label: '更新时间',
		prop: 'updateTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
]
</script>

<template>
	<div class="h-full flex flex-col">
		<!-- 查询表单 -->
		<BizCardsQuery class="mb-sm">
			<BizFormQueryForm :model="queryModel">
				<XFormItem v-if="hasPermission('x:dept:query')" label="负责部门" prop="deptId">
					<XSelect v-model="queryModel.deptId" :remote-method="BIZ_Dept_APIS.getPage" searchable />
				</XFormItem>
				<XFormItem label="所属制度" prop="docId">
					<XSelect v-model="queryModel.docId" :remote-method="BIZ_FxpjSysDoc_APIS.getPage" searchable label-key="docName" />
				</XFormItem>
				<XFormItem label="活动名称/编号" prop="keyword" label-width="7rem">
					<XInput v-model="queryModel.keyword" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
			</BizFormQueryForm>
			<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
				<BizButtonsReset @click="reset" />
				<BizButtonsQuery @click="run" />
			</div>
		</BizCardsQuery>
		
		<!-- 活动类型筛选 -->
		<div class="mb-sm">
			<XRadioGroup v-model="queryModel.actType" tag-group-name="FXPJ活动类型" @change="run" />
		</div>
		
		<!-- 已选择提示 -->
		<div v-if="selectedActivities.length > 0" class="mb-sm p-sm bg-blue-50 rounded border border-blue-200">
			<span class="text-blue-600 font-medium">已选择 {{ selectedActivities.length }} 个控制活动</span>
		</div>
		
		<!-- 表格 -->
		<div class="flex-1 overflow-hidden">
			<BizCardsTable v-loading="loading" class="h-full">
				<BizTablesList 
					ref="BizTablesListRef" 
					:data="list" 
					:columns="columns"
					:selectable="true"
					@update:selected="handleSelectionChange"
				/>
				<div class="flex justify-end mt-base">
					<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
				</div>
			</BizCardsTable>
		</div>
	</div>
</template>

<style scoped>
/* 确保表格容器有合适的高度 */
.h-full {
	height: 100%;
}
</style>