<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="loading || detailLoading" class="pb-base">
			<BizFormForm :model="model" @submit="handleSubmit">
				<XFormItem required label="名称" prop="label">
					<XInput v-model="model.label" autofocus placeholder="请输入名称" @keyup.enter="handleSubmit" />
				</XFormItem>
				<XFormItem label="值" prop="value">
					<XInput v-model="model.value" placeholder="请输入值" />
				</XFormItem>
				<XFormItem label="数值" prop="numberValue">
					<XInput v-model="model.numberValue" placeholder="请输入数值" />
				</XFormItem>
				<XFormItem label="bool值" prop="boolValue">
					<XInput v-model="model.boolValue" placeholder="请输入bool" />
				</XFormItem>
				<XFormItem label="bool值" prop="boolValue">
					<XInput v-model="model.boolValue" placeholder="请输入bool" />
				</XFormItem>
				<XFormItem label="排序" prop="sort">
					<XInput v-model="model.sort" placeholder="请输入排序" />
				</XFormItem>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	label: undefined,
	value: undefined,
	numberValue: undefined,
	boolValue: undefined,
	groupId: undefined,
	sort: undefined,
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
const params = ref({})
let run, loading
function open(_type, _id, _params) {
	console.log('open', _type, _id, _params)
	params.value = _params
	model.value = { groupId: params.value?.groupId, groupName: params.value?.groupName, sort: 100 }
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading: _loading } = xUseRequest(BIZ_CommonTag_APIS[_type], model)
	run = _run
	loading = _loading
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_CommonTag_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res, { groupName: params.value?.groupName, groupId: params.value?.groupId })
}

defineExpose({ open })
async function handleSubmit() {
	await run()
	visible.value = false
	emit('success')
}
</script>

<style scoped></style>
