<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="部门名称" prop="name">
						<XInput v-model="model.name" placeholder="请输入部门名称" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="父部门id" prop="parentId">
						<XInput v-model="model.parentId" placeholder="请输入父部门id" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="显示顺序" prop="sort">
						<XInput v-model="model.sort" placeholder="请输入显示顺序" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="负责人" prop="leaderUserId">
						<XSelect
							v-model="model.leaderUserId"
							searchable
							placeholder="请输入负责人"
							:remote-method="BIZ_Users_APIS.getList"
							label-key="nickname"
							@keyup.enter.stop="handleSubmit"
						/>
					</XFormItem>
					<XFormItem label="联系电话" prop="phone">
						<XInput v-model="model.phone" placeholder="请输入联系电话" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="邮箱" prop="email">
						<XInput v-model="model.email" placeholder="请输入邮箱" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	name: undefined,
	parentId: undefined,
	sort: undefined,
	leaderUserId: undefined,
	phone: undefined,
	email: undefined,
	status: undefined,
	entityId: undefined,
})
const rules = reactive({
	name: [{ required: true, message: '部门名称不能为空' }],
	parentId: [{ required: true, message: '父部门id不能为空' }],
	sort: [{ required: true, message: '显示顺序不能为空' }],
	status: [{ required: true, message: '部门状态（0正常 1停用）不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
const requestLoading = ref(false)
let run, data
const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
const params = ref({})
function open(_type, _id, _params) {
	model.value = { status: 0 }
	params.value = _params
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading, data: _data } = xUseRequest(BIZ_Dept_APIS[_type], model)
	run = _run
	// 监听xUseRequest返回的loading状态，同步到组件级别的loading
	watchEffect(() => {
		requestLoading.value = loading.value
	})
	data = _data
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_Dept_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	if (!visible.value) return
	await run()
	visible.value = false
	if (data && type.value == 'create') {
		model.value.id = data
	}
	emit('success', model.value)
}
</script>

<style scoped></style>
