/**
 * 工厂类型枚举
 */
export enum FactoryType {
	/** 电厂 */
	电厂 = '电厂',
	/** 煤场 */
	煤场 = '煤场',
}

/**
 * 公告类型枚举
 */
export enum AnnouncementType {
	/** 重要通知 */
	IMPORTANT = '重要通知',
	/** 信息 */
	INFO = '信息',
}

/**
 * 公告接口定义
 */
export interface Announcement {
	/** 公告ID */
	id?: number
	/** 公告类型 */
	type: AnnouncementType
	/** 公告内容 */
	content: string
	/** 创建时间 */
	createTime?: string
	/** 更新时间 */
	updateTime?: string
}

/**
 * 通知接口定义（用于横幅显示）
 */
export interface Notification {
	/** 通知ID */
	id: string
	/** 通知消息内容 */
	message: string
	/** 通知类型 */
	type: AnnouncementType
	/** 是否可见 */
	visible: boolean
}

/**
 * 地点基础信息
 */
export interface JnrmTypeLocation {
	/** 地点名称 */
	name: string
	/** 详细地址 */
	address?: string
	/** 经度 */
	longitude?: number
	/** 纬度 */
	latitude?: number
	/** 地点类型（1：出发地，2：目的地，3：中转点） */
	type?: string
	/** 工厂类型 */
	factoryType?: string
}
