import { defineStore } from 'pinia'
import { useWebSocket } from '@vueuse/core'
import { BIZ_JnrmTransportOrder_APIS } from '@/api/biz/admin/jnrmtransportorderapi'
// 车辆基础信息接口
export interface VehicleInfo {
	vehicleId: number
	licensePlate?: string // 车牌号
	driverId?: string
	driverName?: string
	driverPhone?: string
}

// 地点信息接口
export interface Location {
	id: number
	name: string
	address: string
}

// 出发地和目的地类型别名
export type Departure = Location
export type Destination = Location

// GPS位置信息接口
export interface GPSPosition {
	lng: number
	lat: number
	speed?: number
	direction?: number
	province?: string
	city?: string
	country?: string
	address?: string
	gpsTime?: string
}

// GPS原始数据接口（从WebSocket接收的GPS数据）
export interface GPSRawData {
	vehicleId?: number
	lng?: string
	lat?: string
	timestamp?: number
}

// WebSocket消息类型接口
export interface WebSocketMessage {
	// GPS数据消息
	type?: 'gps-data' | 'order-update'
	content?: string // JSON字符串，包含GPS数据数组
	[key: string]: any // 允许其他未知字段
}

// 运单状态枚举
export enum TransportOrderStatus {
	待发车 = '待发车',
	在途 = '在途',
	已送达 = '已送达',
	异常 = '异常',
	取消 = '取消',
}

// 车辆在线状态枚举
export enum VehicleOnlineStatus {
	ONLINE = 'online',
	OFFLINE = 'offline',
}

// 铅封设备信息接口
export interface SealDevice {
	deviceNo: string // 设备编号
	deviceName: string // 设备名称
	vehicleNumber: string // 车牌号
	vehicleName: string // 车辆名称
	position: string // 位置（前、后、上、中等）
	status: number // 状态码（1: 开, 2: 关, 3: 异常等）
	statusName: string // 状态名称
	lastOprtTime: string // 最后操作时间
	address?: string // 地址
	lng?: number // 经度
	lat?: number // 纬度
	companyName?: string // 公司名称
}

// 运单信息接口（主体）
export interface TransportOrder {
	// 运单基础信息
	id: string // 运单ID
	orderNo: string // 运单编号
	powerPlantId?: string // 电厂ID
	departureId?: string // 出发地ID
	departure: Departure // 出发地
	destinationId?: string // 目的地ID
	destination: Destination // 目的地
	status: TransportOrderStatus // 运单状态
	alertNum: number // 告警数

	// 司机信息
	driverId?: number // id
	driverName?: string // 司机姓名
	driverPhone?: string // 司机手机号

	// 货物信息
	cargoInfo?: string // 货物信息
	cargoWeight?: number // 货物重量

	// 时间信息
	startTime?: string // 出发时间
	deliveryTime?: string // 送达时间
	estimatedArrivalTime?: string // 预计到达时间
	actualArrivalTime?: string // 实际到达时间
	createTime?: string // 创建时间
	updateTime?: string // 更新时间

	// 车辆信息（附属）
	vehicleId?: number
	vehicle: VehicleInfo

	// 当前GPS位置（实时）
	currentPosition?: GPSPosition
	lastGPSTime?: string //最后一次GPS更新时间

	// 历史GPS位置（附属）
	historyPositions?: GPSPosition[]

	// 车辆在线状态
	vehicleOnlineStatus?: VehicleOnlineStatus
	offlineTime?: string

	// 行程信息
	mileage?: number // 形成总里程
	runDistance?: number // 已跑距离
	remainDistance?: number // 剩余距离
	estimateArriveTime?: string // 预计到达时间

	// 部门[电厂]id
	deptId?: number

	// 部门[电厂]信息
	dept?: {
		id: number
		name: string
		phone: string
	}

	// 铅封设备列表
	sealListJson?: string
	sealDevices?: SealDevice[]
}

// 运单状态映射（现在key和value都是中文，可以简化）
export const TransportOrderStatusMap = {
	[TransportOrderStatus.待发车]: '待发车',
	[TransportOrderStatus.在途]: '在途',
	[TransportOrderStatus.已送达]: '已送达',
	[TransportOrderStatus.异常]: '异常',
	[TransportOrderStatus.取消]: '取消',
}

// 车辆在线状态映射
export const VehicleOnlineStatusMap = {
	[VehicleOnlineStatus.ONLINE]: '在线',
	[VehicleOnlineStatus.OFFLINE]: '离线',
}

// 生成模拟铅封数据的函数（用于测试，实际项目中应该从API获取）

export const useTransportOrderStore = defineStore('transportOrder', () => {
	// 运单数据映射表 (orderId -> TransportOrder)
	const orders = ref<Map<string, TransportOrder>>(new Map())

	// 当前选中的运单
	const selectedOrder = ref<TransportOrder | null>(null)

	// WebSocket连接状态
	const isConnected = ref(false)
	const connectionError = ref<string | null>(null)

	// WebSocket配置
	const wsUrl = computed(() => {
		const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
		return `${protocol}//` + (import.meta.env.VITE_WS_BASE_URL || `${window.location.hostname}:48080`) + '/infra/ws?token=anonymous-token-123'
	})

	// 计算属性
	// 运单列表
	const orderList = computed(() => Array.from(orders.value.values()))

	// 按状态分组的运单
	const pendingOrders = computed(() => orderList.value.filter((order) => order.status === TransportOrderStatus.待发车))
	const inTransitOrders = computed(() => orderList.value.filter((order) => order.status === TransportOrderStatus.在途))
	const deliveredOrders = computed(() => orderList.value.filter((order) => order.status === TransportOrderStatus.已送达))
	const exceptionOrders = computed(() => orderList.value.filter((order) => order.status === TransportOrderStatus.异常))
	const cancelledOrders = computed(() => orderList.value.filter((order) => order.status === TransportOrderStatus.取消))
	const allUncancelledOrders = computed(() => orderList.value.filter((order) => order.status != TransportOrderStatus.取消))

	// 按车辆在线状态分组
	const onlineOrders = computed(() => orderList.value.filter((order) => order.vehicleOnlineStatus === VehicleOnlineStatus.ONLINE))
	const offlineOrders = computed(() => orderList.value.filter((order) => order.vehicleOnlineStatus === VehicleOnlineStatus.OFFLINE))

	// 统计数量
	const totalCount = computed(() => orders.value.size)
	const pendingCount = computed(() => pendingOrders.value.length)
	const inTransitCount = computed(() => inTransitOrders.value.length)
	const deliveredCount = computed(() => deliveredOrders.value.length)
	const exceptionCount = computed(() => exceptionOrders.value.length)
	const cancelledCount = computed(() => cancelledOrders.value.length)
	const onlineCount = computed(() => onlineOrders.value.length)
	const offlineCount = computed(() => offlineOrders.value.length)

	// WebSocket实例
	let wsInstance: any = null

	// 初始化WebSocket连接
	const initWebSocket = () => {
		if (wsInstance) {
			closeWebSocket()
		}

		const { status, data, send, open, close } = useWebSocket(wsUrl.value, {
			autoReconnect: {
				retries: 5,
				delay: 3000,
				onFailed() {
					console.error('WebSocket重连失败')
					connectionError.value = 'WebSocket连接失败'
				},
			},
			heartbeat: {
				message: 'ping',
				interval: 30000,
			},
		})

		wsInstance = { send, open, close }

		// 监听连接状态
		watch(status, (newStatus) => {
			isConnected.value = newStatus === 'OPEN'
			if (newStatus === 'OPEN') {
				connectionError.value = null
				console.log('WebSocket连接已建立----')
			} else if (newStatus === 'CLOSED') {
				console.log('WebSocket连接已关闭')
			}
		})

		// 监听消息
		watch(data, (newData) => {
			if (newData) {
				handleWebSocketMessage(newData)
			}
		})

		// 建立连接
		open()
	}

	// 关闭WebSocket连接
	const closeWebSocket = () => {
		if (wsInstance) {
			wsInstance.close()
			wsInstance = null
		}
		isConnected.value = false
	}

	// 重连WebSocket
	const reconnectWebSocket = () => {
		closeWebSocket()
		setTimeout(() => {
			initWebSocket()
		}, 3000)
	}

	// 发送消息
	const sendMessage = (message: any) => {
		if (wsInstance && isConnected.value) {
			wsInstance.send(JSON.stringify(message))
		} else {
			console.warn('WebSocket未连接，无法发送消息')
		}
	}

	// 处理接收到的WebSocket消息
	const handleWebSocketMessage = (messageData: string) => {
		// 忽略心跳包回复
		if (messageData === 'pong') {
			return
		}

		try {
			const message: WebSocketMessage = JSON.parse(messageData)

			// 检查消息类型
			if (message.type === 'gps-data') {
				// 处理GPS数据，content字段包含实际的GPS数据数组
				if (message.content) {
					const gpsData: GPSRawData[] = JSON.parse(message.content)
					// console.log('收到新的GPS数据:', gpsData)
					// 遍历处理每条GPS数据
					for (const data of gpsData) {
						handleGPSData(data)
					}
				}
			} else if (message.type === 'order-update') {
				if (message.content) {
					const orderData: TransportOrder = JSON.parse(message.content)
					console.log('收到新的运单变更数据:', orderData)
					handleOrderUpdate(orderData)
				}
			}
		} catch (error) {
			console.error('解析WebSocket消息失败:', error, JSON.parse(messageData))
		}
	}

	// 处理GPS数据更新
	const handleGPSData = (data: GPSRawData) => {
		// 检查必要的GPS数据字段是否存在
		if (!data.vehicleId || !data.lng || !data.lat) {
			return
		}

		// 查找对应的运单（通过vehicleId）
		const existingOrder = findOrderByVehicleId(data.vehicleId)

		if (existingOrder) {
			// 更新现有运单的GPS信息
			updateOrderGPS(existingOrder.id, data)
		}
	}

	// 处理运单状态更新
	const handleOrderUpdate = (orderData: TransportOrder) => {
		if (orderData.id) {
			orders.value.set(orderData.id, orderData)
		}
	}

	// 根据车辆ID查找运单
	const findOrderByVehicleId = (vehicleId: number): TransportOrder | null => {
		for (const order of orders.value.values()) {
			if (order.vehicleId === vehicleId) {
				return order
			}
		}

		return null
	}

	/**
	 * 轮询运单信息，更新到orders.value，逻辑：
	 * 1. 如果当前运单不存在，则添加到orders.value
	 * 2. 如果当前运单已经存在，不要破坏已有的GPS数据，仅仅更新运单的基本信息
	 */
	const pollTransportOrder = async () => {
		try {
			// 调用API获取最新运单列表
			// TODO 更新为实际的API接口
			const response = await BIZ_JnrmTransportOrder_APIS.getPage({ timeRange: '24hours', pageSize: 300 })
			console.log('获取运单列表成功，response:', response)
			const newOrders: TransportOrder[] = response?.list || []

			const plantStore = usePlantStore()
			const plantList = await plantStore.getList()

			// 遍历新的运单数据
			newOrders.forEach((newOrder) => {
				if (newOrder.sealListJson) {
					newOrder.sealDevices = JSON.parse(newOrder.sealListJson)
				}

				newOrder.dept = plantList.find((plant) => plant.id === newOrder.deptId)

				// 为运单添加模拟铅封数据（实际项目中应该从API获取）

				const existingOrder = orders.value.get(newOrder.id)

				if (!existingOrder) {
					// 如果运单不存在，直接添加
					orders.value.set(newOrder.id, newOrder)
				} else {
					// 如果运单已存在，保留GPS相关数据和铅封数据
					const updatedOrder = {
						...newOrder,
						currentPosition: existingOrder.currentPosition,
						historyPositions: existingOrder.historyPositions,
						lastGPSTime: existingOrder.lastGPSTime,
						vehicleOnlineStatus: existingOrder.vehicleOnlineStatus,
						offlineTime: existingOrder.offlineTime,
						sealDevices: existingOrder.sealDevices || newOrder.sealDevices, // 保留已有的铅封数据
					}
					orders.value.set(newOrder.id, updatedOrder)
				}
			})

			console.log('运单数据轮询更新成功，当前运单数量:', orders.value.size)
		} catch (error) {
			console.error('轮询运单数据失败:', error)
		}
	}

	function refreshList() {
		orders.value.clear()
		pollTransportOrder()
	}

	/**
	 * 更新运单的GPS信息
	 * @param orderId 运单ID
	 * @param gpsData GPS原始数据
	 */
	const updateOrderGPS = (orderId: string, gpsData: GPSRawData) => {
		const order = orders.value.get(orderId)
		if (order) {
			// 处理GPS时间
			let gpsTimeString: string = new Date().toISOString()
			if (gpsData.timestamp) {
				gpsTimeString = new Date(gpsData.timestamp).toISOString()
			}

			// 保存当前位置到历史位置
			if (order.currentPosition) {
				if (!order.historyPositions) {
					order.historyPositions = []
				}
				order.historyPositions.push({ ...order.currentPosition })
			}

			// 更新当前GPS位置
			order.currentPosition = {
				// 保留原有的其他位置信息
				...order.currentPosition,
				lng: Number(gpsData.lng),
				lat: Number(gpsData.lat),
				gpsTime: gpsTimeString,
			}

			// 更新车辆在线状态
			order.vehicleOnlineStatus = VehicleOnlineStatus.ONLINE
			order.offlineTime = undefined

			// 更新运单的最后GPS更新时间
			order.lastGPSTime = new Date().toISOString()

			// 更新Map中的运单数据
			orders.value.set(orderId, { ...order })

			console.log(`运单 ${order.orderNo} GPS位置已更新:`, orders.value.get(orderId))
		}
	}

	// 选择运单
	const selectOrder = (orderId: string) => {
		const order = orders.value.get(orderId)
		if (order) {
			selectedOrder.value = order
		}
	}

	// 取消选择运单
	const unselectOrder = () => {
		selectedOrder.value = null
	}

	// 根据运单号模糊搜索，返回所有匹配的运单
	const searchOrderByNo = (orderNo: string): TransportOrder[] => {
		const results: TransportOrder[] = []
		for (const order of orders.value.values()) {
			if (order.orderNo.toLowerCase().includes(orderNo.toLowerCase())) {
				results.push(order)
			}
		}
		return results
	}

	// 根据车牌号搜索
	const searchOrderByLicensePlate = (licensePlate: string): TransportOrder[] => {
		return orderList.value.filter((order) => order.vehicle.licensePlate?.includes(licensePlate))
	}

	// 根据状态过滤运单
	const getOrdersByStatus = (status: TransportOrderStatus): TransportOrder[] => {
		return orderList.value.filter((order) => order.status === status)
	}

	// 清空所有运单数据
	const clearOrders = () => {
		orders.value.clear()
		selectedOrder.value = null
	}

	// 格式化运单状态
	const formatOrderStatus = (status: TransportOrderStatus): string => {
		return TransportOrderStatusMap[status] || '未知'
	}

	// 格式化车辆在线状态
	const formatVehicleOnlineStatus = (status?: VehicleOnlineStatus): string => {
		return status ? VehicleOnlineStatusMap[status] : '未知'
	}

	// 获取运单状态颜色
	const getOrderStatusColor = (order: TransportOrder): string => {
		if (order.vehicleOnlineStatus === VehicleOnlineStatus.OFFLINE) {
			return '#ef4444' // 红色 - 离线
		}

		switch (order.status) {
			case TransportOrderStatus.待发车:
				return '#f59e0b' // 橙色 - 待发车
			case TransportOrderStatus.在途:
				return '#10b981' // 绿色 - 在途
			case TransportOrderStatus.已送达:
				return '#6b7280' // 灰色 - 已送达
			case TransportOrderStatus.异常:
				return '#dc2626' // 深红色 - 异常
			case TransportOrderStatus.取消:
				return '#ef4444' // 红色 - 取消
			default:
				return '#6b7280' // 默认灰色
		}
	}

	// 获取方向文本
	const getDirectionText = (direction?: number): string => {
		if (direction === undefined || direction === null) return '未知'

		const directions = ['北', '东北', '东', '东南', '南', '西南', '西', '西北']

		// 将360度分为8个方向，每个方向45度
		const index = Math.round(direction / 45) % 8
		return directions[index]
	}

	// 格式化预计到达时间
	const formatEstimateArriveTime = (timestamp?: string): string => {
		if (!timestamp) return '--'
		try {
			const date = new Date(parseInt(timestamp))
			return date.toLocaleTimeString('zh-CN', {
				hour: '2-digit',
				minute: '2-digit',
				second: '2-digit',
			})
		} catch {
			return timestamp
		}
	}

	// ============== 任务看板统计相关 ==============

	// 任务完成率（已送达 / 总任务）
	const taskCompletionRate = computed(() => {
		const total = totalCount.value
		if (total === 0) return 0
		return Math.round((deliveredCount.value / total) * 100)
	})

	// 正常运行的运单（在途且在线）
	const normalRunningOrders = computed(() =>
		orderList.value.filter((order) => order.status === TransportOrderStatus.在途 && order.vehicleOnlineStatus === VehicleOnlineStatus.ONLINE),
	)
	const normalRunningCount = computed(() => normalRunningOrders.value.length)

	// 异常停留的运单（在途但离线超过一定时间，或者状态为异常）
	const abnormalStoppedOrders = computed(() =>
		orderList.value.filter(
			(order) =>
				order.status === TransportOrderStatus.异常 ||
				(order.status === TransportOrderStatus.在途 && order.vehicleOnlineStatus === VehicleOnlineStatus.OFFLINE),
		),
	)
	const abnormalStoppedCount = computed(() => abnormalStoppedOrders.value.length)

	// 车辆状态分布百分比
	const vehicleStatusDistribution = computed(() => {
		const total = totalCount.value
		if (total === 0) {
			return {
				在途: 0,
				待发车: 0,
				已送达: 0,
				异常: 0,
				取消: 0,
			}
		}

		return {
			在途: Math.round((inTransitCount.value / total) * 100),
			待发车: Math.round((pendingCount.value / total) * 100),
			已送达: Math.round((deliveredCount.value / total) * 100),
			异常: Math.round((exceptionCount.value / total) * 100),
			取消: Math.round((cancelledCount.value / total) * 100),
		}
	})

	return {
		// 状态
		orders: readonly(orders),
		selectedOrder: readonly(selectedOrder),
		isConnected: readonly(isConnected),
		connectionError: readonly(connectionError),

		// 计算属性
		orderList,
		pendingOrders,
		inTransitOrders,
		deliveredOrders,
		exceptionOrders,
		cancelledOrders,
		allUncancelledOrders,
		onlineOrders,
		offlineOrders,
		totalCount,
		pendingCount,
		inTransitCount,
		deliveredCount,
		exceptionCount,
		cancelledCount,
		onlineCount,
		offlineCount,

		// 任务看板统计
		taskCompletionRate,
		normalRunningOrders,
		normalRunningCount,
		abnormalStoppedOrders,
		abnormalStoppedCount,
		vehicleStatusDistribution,

		// 方法
		initWebSocket,
		closeWebSocket,
		reconnectWebSocket,
		sendMessage,
		handleWebSocketMessage,
		handleGPSData,
		pollTransportOrder,
		handleOrderUpdate,
		selectOrder,
		unselectOrder,
		findOrderByVehicleId,
		searchOrderByNo,
		searchOrderByLicensePlate,
		getOrdersByStatus,
		clearOrders,
		formatOrderStatus,
		formatVehicleOnlineStatus,
		getOrderStatusColor,
		getDirectionText,
		formatEstimateArriveTime,
		refreshList,
	}
})

export default useTransportOrderStore
