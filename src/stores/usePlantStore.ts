import { defineStore } from 'pinia'
import { BIZ_Dept_APIS } from '@/api/biz/admin/deptapi'

export default defineStore('biz-plant', {
	state() {
		return {
			// 初始化标识（参照 useAlertStore）
			isInit: false,
			// 电厂（部门）列表，元素将包含 { id, name, location?: { longitude, latitude } }
			list: [] as any[],
			// 当前选中的电厂（部门）ID
			selectedDeptId: null as number | null,
			// 切换电厂时的全屏 Loading 状态
			isSwitching: false,
		}
	},
	actions: {
		async init() {
			if (this.isInit) return
			this.isInit = true
			await this.refreshList()
		},

		// 获取电厂列表（无轮询）
		async getList() {
			if (!this.isInit) {
				this.isInit = true
				await this.refreshList()
			}
			return this.list
		},

		// 刷新电厂列表
		async refreshList() {
			try {
				this.list = []
				// 使用部门简单列表接口获取电厂（部门）数据
				const res = await BIZ_Dept_APIS.getPageSimple()
				this.list = (Array.isArray(res) ? res : res?.list || res?.data || []) || []
			} catch (error) {
				console.error('获取电厂列表失败:', error)
			}
		},

		// 带有全屏 Loading 的切换电厂
		async switchDept(id: number | null, { minDuration = 850 } = {}) {
			this.isSwitching = true
			await new Promise((resolve) => setTimeout(resolve, Math.max(0, minDuration)))
			this.isSwitching = false
			this.selectedDeptId = id
		},

		// 仅设置电厂（无 Loading）
		setSelectedDeptId(id: number | null) {
			this.selectedDeptId = id
		},

		// 主动控制 Loading（可用于外部复杂流程）
		startSwitching() {
			this.isSwitching = true
		},
		stopSwitching() {
			this.isSwitching = false
		},

		reset() {
			this.selectedDeptId = null
		},

		// 提供给 XSelect 的远程方法（统一由 store 发起部门搜索）
		async remoteSearch(params: any) {
			const res = await BIZ_Dept_APIS.getPageSimple(params)
			const list = Array.isArray(res) ? res : res?.list || res?.data || []
			return list
		},
	},
	persist: false,
})
