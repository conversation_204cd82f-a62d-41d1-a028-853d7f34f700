import { defineStore } from 'pinia'
import type { Announcement, AnnouncementType } from '@/types/biz/biztypes'
import { BIZ_CommonAnnouncement_APIS } from '~/api/biz/admin/commonannouncementapi'

export default defineStore('biz-announcement', {
	state() {
		return {
			isInit: false,
			list: [] as Announcement[],
			isPolling: false,
			pollTimer: null as ReturnType<typeof setInterval> | null,
		}
	},
	actions: {
		async getList() {
			// 如果还未初始化，先初始化数据并启动轮询
			if (!this.isInit) {
				this.isInit = true
				await this.refreshList()
				this.startPolling()
			}
			return this.list
		},

		async refreshList() {
			try {
				const res = await BIZ_CommonAnnouncement_APIS.getPage({ pageSize: 100 })
				this.list = res.list || []
			} catch (error) {
				console.error('获取公告列表失败:', error)
				// 如果API调用失败，使用默认数据
				this.list = this.getDefaultAnnouncements()
			}
		},

		// 获取默认公告数据
		getDefaultAnnouncements(): Announcement[] {
			return [
				{
					id: 1,
					type: AnnouncementType.IMPORTANT,
					content:
						'重要通知：明日预计大风天气，请提前调整运输计划，确保车辆安全。同济高速K135段因施工，部分车道将限行，请车辆注意避开或提前规划路线。',
				},
				{
					id: 2,
					type: AnnouncementType.INFO,
					content: '系统维护通知：今晚22:00-23:00将进行系统升级维护，期间可能影响部分功能使用，请提前做好准备。',
				},
				{
					id: 3,
					type: AnnouncementType.INFO,
					content: '运输提醒：本周末将有大型活动，市区部分道路实施交通管制，建议选择绕行路线或调整运输时间。',
				},
			]
		},
		startPolling() {
			// 如果已经在轮询，则不重复启动
			if (this.isPolling) {
				return
			}

			this.isPolling = true

			// 设置30秒轮询
			this.pollTimer = setInterval(() => {
				this.refreshList()
			}, 30000) // 30秒 = 30000毫秒
		},

		stopPolling() {
			if (this.pollTimer) {
				clearInterval(this.pollTimer)
				this.pollTimer = null
			}
			this.isPolling = false
		},

		// 根据类型获取公告
		getAnnouncementsByType(type: AnnouncementType) {
			return this.list.filter((announcement) => announcement.type === type)
		},

		// 获取重要通知
		getImportantAnnouncements() {
			return this.getAnnouncementsByType(AnnouncementType.IMPORTANT)
		},

		// 获取信息通知
		getInfoAnnouncements() {
			return this.getAnnouncementsByType(AnnouncementType.INFO)
		},
	},
	getters: {
		// 计算属性：公告总数
		announcementCount: (state) => state.list.length,

		// 计算属性：重要通知数量
		importantCount: (state) => state.list.filter((item) => item.type === AnnouncementType.IMPORTANT).length,

		// 计算属性：信息通知数量
		infoCount: (state) => state.list.filter((item) => item.type === AnnouncementType.INFO).length,
	},
})
