import { defineStore } from 'pinia'
import type { JnrmTypeLocation, FactoryType } from '@/types/biz/biztypes'
export default defineStore('biz-location', {
	state() {
		return {
			isInit: false,
			list: [] as JnrmTypeLocation[],
		}
	},
	actions: {
		async getList() {
			// 如果还未初始化，先初始化数据并启动轮询
			if (!this.isInit) {
				this.isInit = true
				await this.refreshList()
			}
			return this.list
		},

		async refreshList() {
			try {
				const res = await BIZ_JnrmLocation_APIS.getList()
				this.list = res || []
			} catch (error) {
				console.error('获取告警列表失败:', error)
			}
		},

		/**
		 * 根据工厂类型获取地点列表
		 * @param factoryType 工厂类型
		 * @returns 过滤后的地点列表
		 */
		async getListByFactoryType(factoryType: FactoryType | string): Promise<JnrmTypeLocation[]> {
			await this.getList()
			return this.list.filter((location) => location.factoryType === factoryType)
		},
	},
	getters: {},
})
