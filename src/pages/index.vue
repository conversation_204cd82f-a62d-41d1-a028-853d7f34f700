<route lang="json">
{
	"name": "webHome",
	"meta": {
		"layout": "empty",
		"title": "欢迎页"
	}
}
</route>
<script setup lang="ts">
import FullscreenTechLoading from '@/components/common/FullscreenTechLoading.vue'
const plantStore = usePlantStore()
const { selectedDeptId } = storeToRefs(plantStore)
const userStore = useUserStore()
</script>

<template>
	<div
		class="grid grid-rows-[auto_auto_1fr_auto] h-screen overflow-hidden text-white"
		style="background: linear-gradient(135deg, #1a2b4d 0%, #101b3d 100%)"
	>
		<!-- 头部 -->
		<BizJnrmHeaderHeaderComponent />

		<!-- 通知横幅 + 电厂选择 -->
		<div class="h-10 flex items-center gap-2 px-2">
			<div class="min-w-0 flex-1">
				<BizJnrmNotificationBanner auto-init />
			</div>
			<div v-if="userStore.isPlatformSuper" class="w-60 flex-shrink-0">
				<!-- 平台超级管理员可选电厂；其他角色可显示其所属电厂，亦可按需隐藏 -->
				<XSelect
					:model-value="selectedDeptId"
					:disabled="plantStore.isSwitching"
					clearable
					:remote-method="plantStore.remoteSearch"
					placeholder="选择电厂"
					@change="(val) => plantStore.switchDept(val ?? null, { minDuration: 1200 })"
				/>
				<FullscreenTechLoading v-if="plantStore.isSwitching" />
			</div>
		</div>

		<!-- 主要内容区域 -->
		<main class="grid grid-cols-[1fr_4fr_1fr] h-full min-h-0 gap-2 p-2">
			<!-- 左侧边栏 -->
			<aside class="grid grid-rows-[1fr_1fr] h-full min-h-0 gap-2">
				<!-- 任务看板区域 -->
				<div class="h-full min-h-0">
					<BizJnrmDashboardTaskBoard />
				</div>

				<!-- 关键指标监控区 -->
				<div class="h-full min-h-0">
					<BizJnrmDashboardKeyMetrics />
				</div>

				<!-- 供需监控 -->
				<!--				<BizJnrmDashboardSupplyDemandMonitor />-->
			</aside>

			<!-- 中央地图区域 -->
			<div class="h-full min-h-0">
				<BizJnrmDashboardMap class="hfull" />
			</div>

			<!-- 右侧边栏 -->
			<aside class="grid grid-rows-[1fr_1fr] h-full min-h-0 gap-2">
				<!-- 异常告警中心 -->
				<div class="h-full min-h-0">
					<BizJnrmDashboardAlertCenter />
				</div>

				<!-- 运单中心 -->
				<div class="h-full min-h-0">
					<BizJnrmDashboardOrderCenter />
				</div>
			</aside>
		</main>

		<!-- 底部信息栏 -->
		<BizJnrmFooterComponent
			:system-status="{ status: 'normal', text: '系统运行正常' }"
			:transport-stats="{ amount: 8526, unit: '吨', period: '今日' }"
			copyright="© 2025 晋能电力"
		/>
	</div>
</template>

<style scoped></style>
